package com.meals.service;

import com.meals.entity.User;

/**
 * 用户业务逻辑接口
 * 定义用户相关的业务操作方法
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface UserService {

    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱（可选）
     * @param phone 手机号（可选）
     * @return 注册成功的用户信息（不包含密码）
     * @throws RuntimeException 当用户名已存在或其他业务异常时抛出
     */
    User register(String username, String password, String email, String phone);

    /**
     * 用户登录
     *
     * @param emailOrPhone 邮箱或手机号
     * @param password 密码
     * @return 登录成功的用户信息（不包含密码）
     * @throws RuntimeException 当邮箱/手机号不存在或密码错误时抛出
     */
    User login(String emailOrPhone, String password);

    /**
     * 根据用户名查询用户信息
     * 
     * @param username 用户名
     * @return 用户信息，如果不存在返回null
     */
    User findByUsername(String username);

    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @return 存在返回true，不存在返回false
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱地址
     * @return 存在返回true，不存在返回false
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否已存在
     * 
     * @param phone 手机号码
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(String phone);

    /**
     * 根据用户ID获取用户信息
     * 
     * @param id 用户ID
     * @return 用户信息（不包含密码），如果不存在返回null
     */
    User getUserById(Long id);
}
