package com.meals.entity;

import java.time.LocalDateTime;

/**
 * 用户健康状况实体类
 * 对应数据库user_health_status表
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserHealthStatus {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 慢性病史（JSON格式）
     */
    private String chronicDiseases;

    /**
     * 过敏信息（JSON格式）
     */
    private String allergies;

    /**
     * 用药记录（JSON格式）
     */
    private String medications;

    /**
     * 手术史（JSON格式）
     */
    private String surgeries;

    /**
     * 家族病史（JSON格式）
     */
    private String familyHistory;

    /**
     * 体检报告（JSON格式）
     */
    private String medicalReports;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // ==================== Getter和Setter方法 ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getChronicDiseases() {
        return chronicDiseases;
    }

    public void setChronicDiseases(String chronicDiseases) {
        this.chronicDiseases = chronicDiseases;
    }

    public String getAllergies() {
        return allergies;
    }

    public void setAllergies(String allergies) {
        this.allergies = allergies;
    }

    public String getMedications() {
        return medications;
    }

    public void setMedications(String medications) {
        this.medications = medications;
    }

    public String getSurgeries() {
        return surgeries;
    }

    public void setSurgeries(String surgeries) {
        this.surgeries = surgeries;
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = familyHistory;
    }

    public String getMedicalReports() {
        return medicalReports;
    }

    public void setMedicalReports(String medicalReports) {
        this.medicalReports = medicalReports;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
