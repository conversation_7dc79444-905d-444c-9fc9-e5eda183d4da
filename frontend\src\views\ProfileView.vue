<template>
  <div class="profile-container">
    <!-- 页面头部 -->
    <div class="profile-header">
      <div class="header-content">
        <div class="user-avatar">
          <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" />
          <div v-else class="avatar-placeholder">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" 
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="user-info">
          <h1>{{ userInfo.name || '未设置姓名' }}</h1>
          <p>{{ userInfo.email }}</p>
          <div class="quick-stats">
            <span v-if="userInfo.age">{{ userInfo.age }}岁</span>
            <span v-if="userInfo.height && userInfo.weight">BMI: {{ calculateBMI() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="profile-content">
      <!-- 左侧导航 -->
      <div class="profile-sidebar">
        <nav class="profile-nav">
          <div 
            v-for="tab in tabs" 
            :key="tab.id"
            :class="['nav-item', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            <div class="nav-icon" v-html="getIconSvg(tab.id)"></div>
            <span>{{ tab.label }}</span>
          </div>
        </nav>
      </div>

      <!-- 右侧内容区域 -->
      <div class="profile-main">
        <!-- 基础信息管理 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <BasicInfoTab 
            v-model:userInfo="userInfo"
            @update="handleDataUpdate"
          />
        </div>

        <!-- 健康状况记录 -->
        <div v-if="activeTab === 'health'" class="tab-content">
          <HealthStatusTab 
            v-model:healthData="healthData"
            @update="handleDataUpdate"
          />
        </div>

        <!-- 饮食偏好设置 -->
        <div v-if="activeTab === 'diet'" class="tab-content">
          <DietPreferenceTab 
            v-model:dietData="dietData"
            @update="handleDataUpdate"
          />
        </div>

        <!-- 营养目标管理 -->
        <div v-if="activeTab === 'goals'" class="tab-content">
          <NutritionGoalsTab 
            v-model:goalsData="goalsData"
            @update="handleDataUpdate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BasicInfoTab from '../components/profile/BasicInfoTab.vue'
import HealthStatusTab from '../components/profile/HealthStatusTab.vue'
import DietPreferenceTab from '../components/profile/DietPreferenceTab.vue'
import NutritionGoalsTab from '../components/profile/NutritionGoalsTab.vue'

// 图标SVG字符串
const iconSvgs = {
  basic: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
  health: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.84 4.61C19.5 3.28 17.45 3.28 16.11 4.61L12 8.72L7.89 4.61C6.55 3.28 4.5 3.28 3.16 4.61C1.82 5.95 1.82 8 3.16 9.34L12 18.18L20.84 9.34C22.18 8 22.18 5.95 20.84 4.61Z"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
  diet: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18 8H22V14C22 15.1046 21.1046 16 20 16H18V8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 8H6V16H4C2.89543 16 2 15.1046 2 14V8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 8V6C6 3.79086 7.79086 2 10 2H14C16.2091 2 18 3.79086 18 6V8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 16V20C6 21.1046 6.89543 22 8 22H16C17.1046 22 18 21.1046 18 20V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
  goals: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
    <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="2"/>
    <circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="2"/>
  </svg>`
}

// 响应式数据
const router = useRouter()
const activeTab = ref('basic')

// 标签页配置
const tabs = [
  { id: 'basic', label: '基础信息' },
  { id: 'health', label: '健康状况' },
  { id: 'diet', label: '饮食偏好' },
  { id: 'goals', label: '营养目标' }
]

// 获取图标SVG
const getIconSvg = (tabId: string) => {
  return iconSvgs[tabId] || ''
}

// 用户基础信息
const userInfo = reactive({
  name: '',
  email: '',
  gender: '',
  birthDate: '',
  height: null,
  weight: null,
  avatar: '',
  bodyFat: null,
  muscleMass: null,
  age: null
})

// 健康状况数据
const healthData = reactive({
  chronicDiseases: [],
  allergies: [],
  medications: [],
  surgeries: [],
  familyHistory: [],
  medicalReports: []
})

// 饮食偏好数据
const dietData = reactive({
  restrictions: [],
  allergies: [],
  preferences: {},
  forbiddenFoods: [],
  favoriteFoods: []
})

// 营养目标数据
const goalsData = reactive({
  weightGoal: {},
  nutritionGoals: {},
  diseaseManagement: {}
})

// 计算BMI
const calculateBMI = () => {
  if (!userInfo.height || !userInfo.weight) return '--'
  const heightInM = userInfo.height / 100
  const bmi = userInfo.weight / (heightInM * heightInM)
  return bmi.toFixed(1)
}

// 处理数据更新
const handleDataUpdate = () => {
  // 保存到本地存储
  localStorage.setItem('userProfile', JSON.stringify({
    userInfo,
    healthData,
    dietData,
    goalsData
  }))
}

// 加载用户数据
const loadUserData = () => {
  // 从localStorage加载数据
  const savedData = localStorage.getItem('userProfile')
  if (savedData) {
    const data = JSON.parse(savedData)
    Object.assign(userInfo, data.userInfo || {})
    Object.assign(healthData, data.healthData || {})
    Object.assign(dietData, data.dietData || {})
    Object.assign(goalsData, data.goalsData || {})
  }

  // 从登录信息获取基础数据
  const loginUser = localStorage.getItem('user')
  if (loginUser) {
    const user = JSON.parse(loginUser)
    userInfo.email = user.email
    if (!userInfo.name) userInfo.name = user.username
  }
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
/* 主容器 */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 页面头部 */
.profile-header {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.15);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 0.8);
}

.user-info h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.user-info p {
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.quick-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 主要内容区域 */
.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
}

/* 左侧导航 */
.profile-sidebar {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.nav-item:hover {
  background: #f1f5f9;
  color: #16a085;
}

.nav-item.active {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
}

/* 右侧内容区域 */
.profile-main {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .profile-sidebar {
    order: 2;
  }
  
  .profile-nav {
    flex-direction: row;
    overflow-x: auto;
  }
  
  .nav-item {
    flex-shrink: 0;
    min-width: 120px;
  }
}
</style>
