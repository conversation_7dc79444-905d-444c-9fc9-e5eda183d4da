package com.meals.controller;

import com.meals.common.LoginRequest;
import com.meals.common.RegisterRequest;
import com.meals.common.Result;
import com.meals.entity.User;
import com.meals.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 * 提供用户相关的REST API接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*") // 允许跨域访问
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册接口
     * 
     * @param request 注册请求参数
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody RegisterRequest request) {
        try {
            User user = userService.register(
                request.getUsername(),
                request.getPassword(),
                request.getEmail(),
                request.getPhone()
            );
            return Result.success("注册成功", user);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("注册失败，请稍后重试");
        }
    }

    /**
     * 用户登录接口
     * 
     * @param request 登录请求参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<User> login(@Valid @RequestBody LoginRequest request) {
        try {
            User user = userService.login(request.getEmailOrPhone(), request.getPassword());
            return Result.success("登录成功", user);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("登录失败，请稍后重试");
        }
    }

    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 检查结果
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.existsByUsername(username);
            return Result.success("检查完成", !exists); // 返回true表示可用，false表示已存在
        } catch (Exception e) {
            return Result.error("检查失败，请稍后重试");
        }
    }

    /**
     * 检查邮箱是否可用
     * 
     * @param email 邮箱地址
     * @return 检查结果
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.existsByEmail(email);
            return Result.success("检查完成", !exists); // 返回true表示可用，false表示已存在
        } catch (Exception e) {
            return Result.error("检查失败，请稍后重试");
        }
    }

    /**
     * 检查手机号是否可用
     * 
     * @param phone 手机号码
     * @return 检查结果
     */
    @GetMapping("/check-phone")
    public Result<Boolean> checkPhone(@RequestParam String phone) {
        try {
            boolean exists = userService.existsByPhone(phone);
            return Result.success("检查完成", !exists); // 返回true表示可用，false表示已存在
        } catch (Exception e) {
            return Result.error("检查失败，请稍后重试");
        }
    }

    /**
     * 根据用户ID获取用户信息
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        try {
            User user = userService.getUserById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            return Result.success("获取成功", user);
        } catch (Exception e) {
            return Result.error("获取用户信息失败");
        }
    }
}
