<template>
  <div class="health-status-tab">
    <div class="tab-header">
      <h2>健康状况记录</h2>
      <p>记录和管理您的健康状况信息</p>
    </div>

    <div class="health-sections">
      <!-- 慢性病史 -->
      <div class="health-section">
        <h3>慢性病史</h3>
        <div class="condition-list">
          <div 
            v-for="(disease, index) in localHealthData.chronicDiseases" 
            :key="index"
            class="condition-item"
          >
            <div class="condition-info">
              <span class="condition-name">{{ disease.name }}</span>
              <span class="condition-date">确诊时间: {{ disease.diagnosedDate }}</span>
              <span class="condition-status" :class="disease.status">{{ getStatusText(disease.status) }}</span>
            </div>
            <button @click="removeChronicDisease(index)" class="remove-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="add-condition">
          <div class="form-row">
            <select v-model="newDisease.name">
              <option value="">选择疾病类型</option>
              <option value="糖尿病">糖尿病</option>
              <option value="高血压">高血压</option>
              <option value="心脏病">心脏病</option>
              <option value="高血脂">高血脂</option>
              <option value="甲状腺疾病">甲状腺疾病</option>
              <option value="其他">其他</option>
            </select>
            <input 
              v-if="newDisease.name === '其他'" 
              v-model="newDisease.customName" 
              type="text" 
              placeholder="请输入疾病名称"
            />
            <input v-model="newDisease.diagnosedDate" type="date" />
            <select v-model="newDisease.status">
              <option value="active">治疗中</option>
              <option value="controlled">已控制</option>
              <option value="cured">已治愈</option>
            </select>
            <button @click="addChronicDisease" class="add-btn">添加</button>
          </div>
        </div>
      </div>

      <!-- 过敏信息 -->
      <div class="health-section">
        <h3>过敏信息</h3>
        <div class="allergy-types">
          <div class="allergy-category">
            <h4>食物过敏</h4>
            <div class="allergy-tags">
              <div 
                v-for="allergy in foodAllergies" 
                :key="allergy"
                :class="['allergy-tag', { active: localHealthData.allergies.includes(allergy) }]"
                @click="toggleAllergy(allergy)"
              >
                {{ allergy }}
              </div>
            </div>
          </div>
          
          <div class="allergy-category">
            <h4>药物过敏</h4>
            <div class="allergy-tags">
              <div 
                v-for="allergy in drugAllergies" 
                :key="allergy"
                :class="['allergy-tag', { active: localHealthData.allergies.includes(allergy) }]"
                @click="toggleAllergy(allergy)"
              >
                {{ allergy }}
              </div>
            </div>
          </div>
          
          <div class="allergy-category">
            <h4>环境过敏</h4>
            <div class="allergy-tags">
              <div 
                v-for="allergy in environmentAllergies" 
                :key="allergy"
                :class="['allergy-tag', { active: localHealthData.allergies.includes(allergy) }]"
                @click="toggleAllergy(allergy)"
              >
                {{ allergy }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="custom-allergy">
          <input 
            v-model="customAllergy" 
            type="text" 
            placeholder="添加其他过敏原"
            @keyup.enter="addCustomAllergy"
          />
          <button @click="addCustomAllergy" class="add-btn">添加</button>
        </div>
      </div>

      <!-- 当前用药 -->
      <div class="health-section">
        <h3>当前用药情况</h3>
        <div class="medication-list">
          <div 
            v-for="(medication, index) in localHealthData.medications" 
            :key="index"
            class="medication-item"
          >
            <div class="medication-info">
              <span class="medication-name">{{ medication.name }}</span>
              <span class="medication-dosage">{{ medication.dosage }}</span>
              <span class="medication-frequency">{{ medication.frequency }}</span>
              <span class="medication-purpose">用途: {{ medication.purpose }}</span>
            </div>
            <button @click="removeMedication(index)" class="remove-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="add-medication">
          <div class="form-grid">
            <input v-model="newMedication.name" type="text" placeholder="药物名称" />
            <input v-model="newMedication.dosage" type="text" placeholder="剂量 (如: 5mg)" />
            <select v-model="newMedication.frequency">
              <option value="">服用频率</option>
              <option value="每日一次">每日一次</option>
              <option value="每日两次">每日两次</option>
              <option value="每日三次">每日三次</option>
              <option value="按需服用">按需服用</option>
            </select>
            <input v-model="newMedication.purpose" type="text" placeholder="用药目的" />
          </div>
          <button @click="addMedication" class="add-btn full-width">添加药物</button>
        </div>
      </div>

      <!-- 手术史 -->
      <div class="health-section">
        <h3>手术史记录</h3>
        <div class="surgery-list">
          <div 
            v-for="(surgery, index) in localHealthData.surgeries" 
            :key="index"
            class="surgery-item"
          >
            <div class="surgery-info">
              <span class="surgery-name">{{ surgery.name }}</span>
              <span class="surgery-date">手术时间: {{ surgery.date }}</span>
              <span class="surgery-hospital">医院: {{ surgery.hospital }}</span>
              <span class="surgery-notes" v-if="surgery.notes">备注: {{ surgery.notes }}</span>
            </div>
            <button @click="removeSurgery(index)" class="remove-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="add-surgery">
          <div class="form-grid">
            <input v-model="newSurgery.name" type="text" placeholder="手术名称" />
            <input v-model="newSurgery.date" type="date" />
            <input v-model="newSurgery.hospital" type="text" placeholder="医院名称" />
            <textarea v-model="newSurgery.notes" placeholder="备注信息（可选）" rows="2"></textarea>
          </div>
          <button @click="addSurgery" class="add-btn full-width">添加手术记录</button>
        </div>
      </div>

      <!-- 体检报告 -->
      <div class="health-section">
        <h3>体检报告管理</h3>
        <div class="report-list">
          <div 
            v-for="(report, index) in localHealthData.medicalReports" 
            :key="index"
            class="report-item"
          >
            <div class="report-info">
              <span class="report-name">{{ report.name }}</span>
              <span class="report-date">{{ report.date }}</span>
              <span class="report-hospital">{{ report.hospital }}</span>
            </div>
            <div class="report-actions">
              <button @click="viewReport(report)" class="view-btn">查看</button>
              <button @click="removeReport(index)" class="remove-btn">删除</button>
            </div>
          </div>
        </div>
        
        <div class="upload-report">
          <input 
            ref="reportInput" 
            type="file" 
            accept=".pdf,.jpg,.jpeg,.png" 
            style="display: none"
            @change="handleReportUpload"
          />
          <button @click="$refs.reportInput.click()" class="upload-btn">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" 
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <polyline points="7,10 12,5 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="12" y1="5" x2="12" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            上传体检报告
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
const props = defineProps<{
  healthData: any
}>()

// Emits
const emit = defineEmits<{
  update: []
}>()

// 本地数据
const localHealthData = ref({ ...props.healthData })

// 新增数据的临时对象
const newDisease = ref({
  name: '',
  customName: '',
  diagnosedDate: '',
  status: 'active'
})

const newMedication = ref({
  name: '',
  dosage: '',
  frequency: '',
  purpose: ''
})

const newSurgery = ref({
  name: '',
  date: '',
  hospital: '',
  notes: ''
})

const customAllergy = ref('')
const reportInput = ref<HTMLInputElement>()

// 过敏原选项
const foodAllergies = ['花生', '坚果', '海鲜', '牛奶', '鸡蛋', '大豆', '小麦', '芝麻']
const drugAllergies = ['青霉素', '阿司匹林', '磺胺类', '头孢类', '碘剂']
const environmentAllergies = ['花粉', '尘螨', '动物毛发', '霉菌', '乳胶']

// 监听props变化
watch(() => props.healthData, (newVal) => {
  localHealthData.value = { ...newVal }
}, { deep: true })

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    active: '治疗中',
    controlled: '已控制',
    cured: '已治愈'
  }
  return statusMap[status] || status
}

// 添加慢性病
const addChronicDisease = () => {
  if (!newDisease.value.name || !newDisease.value.diagnosedDate) return
  
  const diseaseName = newDisease.value.name === '其他' ? newDisease.value.customName : newDisease.value.name
  if (!diseaseName) return
  
  localHealthData.value.chronicDiseases.push({
    name: diseaseName,
    diagnosedDate: newDisease.value.diagnosedDate,
    status: newDisease.value.status
  })
  
  // 重置表单
  newDisease.value = {
    name: '',
    customName: '',
    diagnosedDate: '',
    status: 'active'
  }
  
  handleUpdate()
}

// 删除慢性病
const removeChronicDisease = (index: number) => {
  localHealthData.value.chronicDiseases.splice(index, 1)
  handleUpdate()
}

// 切换过敏原
const toggleAllergy = (allergy: string) => {
  const index = localHealthData.value.allergies.indexOf(allergy)
  if (index > -1) {
    localHealthData.value.allergies.splice(index, 1)
  } else {
    localHealthData.value.allergies.push(allergy)
  }
  handleUpdate()
}

// 添加自定义过敏原
const addCustomAllergy = () => {
  if (!customAllergy.value.trim()) return
  if (!localHealthData.value.allergies.includes(customAllergy.value)) {
    localHealthData.value.allergies.push(customAllergy.value)
    customAllergy.value = ''
    handleUpdate()
  }
}

// 添加药物
const addMedication = () => {
  if (!newMedication.value.name || !newMedication.value.dosage || !newMedication.value.frequency) return
  
  localHealthData.value.medications.push({ ...newMedication.value })
  
  // 重置表单
  newMedication.value = {
    name: '',
    dosage: '',
    frequency: '',
    purpose: ''
  }
  
  handleUpdate()
}

// 删除药物
const removeMedication = (index: number) => {
  localHealthData.value.medications.splice(index, 1)
  handleUpdate()
}

// 添加手术记录
const addSurgery = () => {
  if (!newSurgery.value.name || !newSurgery.value.date || !newSurgery.value.hospital) return
  
  localHealthData.value.surgeries.push({ ...newSurgery.value })
  
  // 重置表单
  newSurgery.value = {
    name: '',
    date: '',
    hospital: '',
    notes: ''
  }
  
  handleUpdate()
}

// 删除手术记录
const removeSurgery = (index: number) => {
  localHealthData.value.surgeries.splice(index, 1)
  handleUpdate()
}

// 处理体检报告上传
const handleReportUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      localHealthData.value.medicalReports.push({
        name: file.name,
        date: new Date().toISOString().split('T')[0],
        hospital: '待填写',
        file: e.target?.result as string
      })
      handleUpdate()
    }
    reader.readAsDataURL(file)
  }
}

// 查看报告
const viewReport = (report: any) => {
  if (report.file) {
    const newWindow = window.open()
    if (newWindow) {
      newWindow.document.write(`<img src="${report.file}" style="max-width: 100%; height: auto;" />`)
    }
  }
}

// 删除报告
const removeReport = (index: number) => {
  localHealthData.value.medicalReports.splice(index, 1)
  handleUpdate()
}

// 处理数据更新
const handleUpdate = () => {
  emit('update')
}

// 监听本地数据变化，同步到父组件
watch(localHealthData, (newVal) => {
  Object.assign(props.healthData, newVal)
}, { deep: true })

// 初始化数据结构
if (!localHealthData.value.chronicDiseases) localHealthData.value.chronicDiseases = []
if (!localHealthData.value.allergies) localHealthData.value.allergies = []
if (!localHealthData.value.medications) localHealthData.value.medications = []
if (!localHealthData.value.surgeries) localHealthData.value.surgeries = []
if (!localHealthData.value.medicalReports) localHealthData.value.medicalReports = []
</script>

<style scoped>
.health-status-tab {
  max-width: 900px;
}

.tab-header {
  margin-bottom: 2rem;
}

.tab-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.tab-header p {
  margin: 0;
  color: #64748b;
}

.health-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.health-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.health-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 条件列表样式 */
.condition-list,
.medication-list,
.surgery-list,
.report-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.condition-item,
.medication-item,
.surgery-item,
.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.condition-info,
.medication-info,
.surgery-info,
.report-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.condition-name,
.medication-name,
.surgery-name,
.report-name {
  font-weight: 600;
  color: #2d3748;
}

.condition-date,
.medication-dosage,
.surgery-date,
.report-date {
  font-size: 0.9rem;
  color: #64748b;
}

.condition-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  width: fit-content;
}

.condition-status.active {
  background: #fef3c7;
  color: #92400e;
}

.condition-status.controlled {
  background: #dcfce7;
  color: #166534;
}

.condition-status.cured {
  background: #dbeafe;
  color: #1e40af;
}

/* 表单样式 */
.form-row,
.form-grid {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.form-row {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

.form-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.form-row input,
.form-row select,
.form-grid input,
.form-grid select,
.form-grid textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
}

/* 过敏标签样式 */
.allergy-types {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.allergy-category h4 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 1rem;
}

.allergy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.allergy-tag {
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.allergy-tag:hover {
  background: #e5e7eb;
}

.allergy-tag.active {
  background: #16a085;
  color: white;
  border-color: #16a085;
}

.custom-allergy {
  display: flex;
  gap: 0.75rem;
}

.custom-allergy input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

/* 按钮样式 */
.add-btn,
.upload-btn,
.view-btn {
  padding: 0.75rem 1rem;
  background: #16a085;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-btn:hover,
.upload-btn:hover,
.view-btn:hover {
  background: #138d75;
}

.add-btn.full-width {
  width: 100%;
  justify-content: center;
}

.remove-btn {
  padding: 0.5rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #dc2626;
}

.remove-btn svg {
  width: 16px;
  height: 16px;
}

.upload-btn svg {
  width: 16px;
  height: 16px;
}

.report-actions {
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .form-row,
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .condition-item,
  .medication-item,
  .surgery-item,
  .report-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .report-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
