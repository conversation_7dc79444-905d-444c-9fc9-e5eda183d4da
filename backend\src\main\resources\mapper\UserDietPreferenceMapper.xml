<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meals.mapper.UserDietPreferenceMapper">

    <!-- 用户饮食偏好结果映射 -->
    <resultMap id="BaseResultMap" type="UserDietPreference">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="vegetarian_type" property="vegetarianType" jdbcType="VARCHAR"/>
        <result column="restrictions" property="restrictions" jdbcType="VARCHAR"/>
        <result column="allergies" property="allergies" jdbcType="VARCHAR"/>
        <result column="preferences" property="preferences" jdbcType="VARCHAR"/>
        <result column="forbidden_foods" property="forbiddenFoods" jdbcType="VARCHAR"/>
        <result column="favorite_foods" property="favoriteFoods" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, vegetarian_type, restrictions, allergies, preferences, 
        forbidden_foods, favorite_foods, create_time, update_time
    </sql>

    <!-- 插入用户饮食偏好 -->
    <insert id="insert" parameterType="UserDietPreference" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_diet_preference (user_id, vegetarian_type, restrictions, allergies, preferences, forbidden_foods, favorite_foods, create_time, update_time)
        VALUES (#{userId}, #{vegetarianType}, #{restrictions}, #{allergies}, #{preferences}, #{forbiddenFoods}, #{favoriteFoods}, NOW(), NOW())
    </insert>

    <!-- 根据用户ID查询饮食偏好 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_diet_preference
        WHERE user_id = #{userId}
    </select>

    <!-- 更新用户饮食偏好 -->
    <update id="updateByUserId" parameterType="UserDietPreference">
        UPDATE user_diet_preference
        SET
            vegetarian_type = #{vegetarianType},
            restrictions = #{restrictions},
            allergies = #{allergies},
            preferences = #{preferences},
            forbidden_foods = #{forbiddenFoods},
            favorite_foods = #{favoriteFoods},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 根据用户ID删除饮食偏好记录 -->
    <delete id="deleteByUserId">
        DELETE FROM user_diet_preference
        WHERE user_id = #{userId}
    </delete>

</mapper>
