package com.meals.service.impl;

import com.meals.entity.User;
import com.meals.mapper.UserMapper;
import com.meals.service.UserService;
import com.meals.utils.PasswordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 用户业务逻辑实现类
 * 实现用户相关的具体业务逻辑
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱（可选）
     * @param phone 手机号（可选）
     * @return 注册成功的用户信息（不包含密码）
     * @throws RuntimeException 当用户名已存在或其他业务异常时抛出
     */
    @Override
    public User register(String username, String password, String email, String phone) {
        // 参数验证
        if (!StringUtils.hasText(username)) {
            throw new RuntimeException("用户名不能为空");
        }
        if (!StringUtils.hasText(password)) {
            throw new RuntimeException("密码不能为空");
        }
        if (username.length() < 3 || username.length() > 20) {
            throw new RuntimeException("用户名长度必须在3-20个字符之间");
        }
        if (password.length() < 6) {
            throw new RuntimeException("密码长度不能少于6个字符");
        }

        // 检查用户名是否已存在
        if (existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在（如果提供了邮箱）
        if (StringUtils.hasText(email) && existsByEmail(email)) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 检查手机号是否已存在（如果提供了手机号）
        if (StringUtils.hasText(phone) && existsByPhone(phone)) {
            throw new RuntimeException("手机号已被注册");
        }

        // 创建用户对象
        User user = new User();
        user.setUsername(username);
        user.setPassword(PasswordUtil.encryptPassword(password)); // 加密密码
        user.setEmail(email);
        user.setPhone(phone);
        user.setStatus(1); // 默认启用状态
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 保存用户
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new RuntimeException("用户注册失败");
        }

        // 返回用户信息（不包含密码）
        user.setPassword(null);
        return user;
    }

    /**
     * 用户登录
     *
     * @param emailOrPhone 邮箱或手机号
     * @param password 密码
     * @return 登录成功的用户信息（不包含密码）
     * @throws RuntimeException 当邮箱/手机号不存在或密码错误时抛出
     */
    @Override
    public User login(String emailOrPhone, String password) {
        // 参数验证
        if (!StringUtils.hasText(emailOrPhone)) {
            throw new RuntimeException("邮箱或手机号不能为空");
        }
        if (!StringUtils.hasText(password)) {
            throw new RuntimeException("密码不能为空");
        }

        // 判断输入的是邮箱还是手机号，并查询用户
        User user = null;
        if (isEmail(emailOrPhone)) {
            user = userMapper.findByEmail(emailOrPhone);
            if (user == null) {
                throw new RuntimeException("邮箱不存在");
            }
        } else if (isPhone(emailOrPhone)) {
            user = userMapper.findByPhone(emailOrPhone);
            if (user == null) {
                throw new RuntimeException("手机号不存在");
            }
        } else {
            throw new RuntimeException("请输入有效的邮箱地址或手机号");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new RuntimeException("用户账号已被禁用");
        }

        // 验证密码
        if (!PasswordUtil.verifyPassword(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 返回用户信息（不包含密码）
        user.setPassword(null);
        return user;
    }

    /**
     * 根据用户名查询用户信息
     * 
     * @param username 用户名
     * @return 用户信息，如果不存在返回null
     */
    @Override
    public User findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        return userMapper.findByUsername(username);
    }

    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return userMapper.existsByUsername(username);
    }

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱地址
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return userMapper.existsByEmail(email);
    }

    /**
     * 检查手机号是否已存在
     * 
     * @param phone 手机号码
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return userMapper.existsByPhone(phone);
    }

    /**
     * 根据用户ID获取用户信息
     * 
     * @param id 用户ID
     * @return 用户信息（不包含密码），如果不存在返回null
     */
    @Override
    public User getUserById(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        User user = userMapper.selectById(id);
        if (user != null) {
            user.setPassword(null); // 不返回密码
        }
        return user;
    }

    /**
     * 判断字符串是否为邮箱格式
     *
     * @param str 待判断的字符串
     * @return 是否为邮箱格式
     */
    private boolean isEmail(String str) {
        return str != null && str.matches("^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$");
    }

    /**
     * 判断字符串是否为手机号格式
     *
     * @param str 待判断的字符串
     * @return 是否为手机号格式
     */
    private boolean isPhone(String str) {
        return str != null && str.matches("^1[3-9]\\d{9}$");
    }
}
