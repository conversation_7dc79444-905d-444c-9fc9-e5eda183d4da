import { post, get } from './api'

// 类型定义
export interface LoginRequest {
  emailOrPhone: string
  password: string
}

export interface RegisterRequest {
  username: string
  password: string
  email: string
  phone: string
}

export interface User {
  id: number
  username: string
  email?: string
  phone?: string
  age?: number
  gender?: number
  height?: number
  weight?: number
  status: number
  createTime: string
  updateTime: string
}

export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 用户登录
export function login(loginData: LoginRequest): Promise<ApiResponse<User>> {
  return post<ApiResponse<User>>('/user/login', loginData)
}

// 用户注册
export function register(registerData: RegisterRequest): Promise<ApiResponse<User>> {
  return post<ApiResponse<User>>('/user/register', registerData)
}

// 检查用户名是否可用
export function checkUsername(username: string): Promise<ApiResponse<boolean>> {
  return get<ApiResponse<boolean>>(`/user/check-username?username=${encodeURIComponent(username)}`)
}

// 检查邮箱是否可用
export function checkEmail(email: string): Promise<ApiResponse<boolean>> {
  return get<ApiResponse<boolean>>(`/user/check-email?email=${encodeURIComponent(email)}`)
}

// 检查手机号是否可用
export function checkPhone(phone: string): Promise<ApiResponse<boolean>> {
  return get<ApiResponse<boolean>>(`/user/check-phone?phone=${encodeURIComponent(phone)}`)
}

// 根据ID获取用户信息
export function getUserById(id: number): Promise<ApiResponse<User>> {
  return get<ApiResponse<User>>(`/user/${id}`)
}
