package com.meals.dto;

import java.time.LocalDate;

/**
 * 用户档案信息DTO
 * 用于前端和后端之间的用户完整档案数据传输
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserProfileDTO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 真实姓名
     */
    private String name;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 用户年龄
     */
    private Integer age;

    /**
     * 用户性别：0-女，1-男
     */
    private Integer gender;

    /**
     * 身高（厘米）
     */
    private Double height;

    /**
     * 体重（公斤）
     */
    private Double weight;

    /**
     * 体脂率（%）
     */
    private Double bodyFat;

    /**
     * 肌肉量（公斤）
     */
    private Double muscleMass;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 健康状况数据
     */
    private UserHealthStatusDTO healthData;

    /**
     * 饮食偏好数据
     */
    private UserDietPreferenceDTO dietData;

    /**
     * 营养目标数据
     */
    private UserNutritionGoalsDTO goalsData;

    // ==================== Getter和Setter方法 ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getBodyFat() {
        return bodyFat;
    }

    public void setBodyFat(Double bodyFat) {
        this.bodyFat = bodyFat;
    }

    public Double getMuscleMass() {
        return muscleMass;
    }

    public void setMuscleMass(Double muscleMass) {
        this.muscleMass = muscleMass;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public UserHealthStatusDTO getHealthData() {
        return healthData;
    }

    public void setHealthData(UserHealthStatusDTO healthData) {
        this.healthData = healthData;
    }

    public UserDietPreferenceDTO getDietData() {
        return dietData;
    }

    public void setDietData(UserDietPreferenceDTO dietData) {
        this.dietData = dietData;
    }

    public UserNutritionGoalsDTO getGoalsData() {
        return goalsData;
    }

    public void setGoalsData(UserNutritionGoalsDTO goalsData) {
        this.goalsData = goalsData;
    }
}
