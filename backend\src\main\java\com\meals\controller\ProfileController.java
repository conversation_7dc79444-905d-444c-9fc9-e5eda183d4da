package com.meals.controller;

import com.meals.common.Result;
import com.meals.dto.*;
import com.meals.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户档案控制器
 * 提供用户档案相关的REST API接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/profile")
@CrossOrigin(origins = "*") // 允许跨域访问
public class ProfileController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取用户完整档案信息
     * 
     * @param userId 用户ID
     * @return 用户完整档案信息
     */
    @GetMapping("/{userId}")
    public Result<UserProfileDTO> getUserProfile(@PathVariable Long userId) {
        try {
            UserProfileDTO profile = userProfileService.getUserProfile(userId);
            return Result.success("获取用户档案成功", profile);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("获取用户档案失败，请稍后重试");
        }
    }

    /**
     * 更新用户基础信息
     * 
     * @param userId 用户ID
     * @param profileDTO 用户档案信息
     * @return 更新后的用户档案信息
     */
    @PutMapping("/{userId}/basic")
    public Result<UserProfileDTO> updateUserBasicInfo(
            @PathVariable Long userId, 
            @RequestBody UserProfileDTO profileDTO) {
        try {
            UserProfileDTO updatedProfile = userProfileService.updateUserBasicInfo(userId, profileDTO);
            return Result.success("更新基础信息成功", updatedProfile);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新基础信息失败，请稍后重试");
        }
    }

    /**
     * 更新用户健康状况
     * 
     * @param userId 用户ID
     * @param healthStatusDTO 健康状况信息
     * @return 更新后的健康状况信息
     */
    @PutMapping("/{userId}/health")
    public Result<UserHealthStatusDTO> updateUserHealthStatus(
            @PathVariable Long userId, 
            @RequestBody UserHealthStatusDTO healthStatusDTO) {
        try {
            UserHealthStatusDTO updatedHealthStatus = userProfileService.updateUserHealthStatus(userId, healthStatusDTO);
            return Result.success("更新健康状况成功", updatedHealthStatus);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新健康状况失败，请稍后重试");
        }
    }

    /**
     * 更新用户饮食偏好
     * 
     * @param userId 用户ID
     * @param dietPreferenceDTO 饮食偏好信息
     * @return 更新后的饮食偏好信息
     */
    @PutMapping("/{userId}/diet")
    public Result<UserDietPreferenceDTO> updateUserDietPreference(
            @PathVariable Long userId, 
            @RequestBody UserDietPreferenceDTO dietPreferenceDTO) {
        try {
            UserDietPreferenceDTO updatedDietPreference = userProfileService.updateUserDietPreference(userId, dietPreferenceDTO);
            return Result.success("更新饮食偏好成功", updatedDietPreference);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新饮食偏好失败，请稍后重试");
        }
    }

    /**
     * 更新用户营养目标
     * 
     * @param userId 用户ID
     * @param nutritionGoalsDTO 营养目标信息
     * @return 更新后的营养目标信息
     */
    @PutMapping("/{userId}/goals")
    public Result<UserNutritionGoalsDTO> updateUserNutritionGoals(
            @PathVariable Long userId, 
            @RequestBody UserNutritionGoalsDTO nutritionGoalsDTO) {
        try {
            UserNutritionGoalsDTO updatedNutritionGoals = userProfileService.updateUserNutritionGoals(userId, nutritionGoalsDTO);
            return Result.success("更新营养目标成功", updatedNutritionGoals);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新营养目标失败，请稍后重试");
        }
    }

    /**
     * 上传用户头像
     * 
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     */
    @PostMapping("/{userId}/avatar")
    public Result<String> uploadUserAvatar(
            @PathVariable Long userId, 
            @RequestParam("file") MultipartFile file) {
        try {
            String avatarUrl = userProfileService.uploadUserAvatar(userId, file);
            return Result.success("头像上传成功", avatarUrl);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("头像上传失败，请稍后重试");
        }
    }

    /**
     * 删除用户头像
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{userId}/avatar")
    public Result<Boolean> deleteUserAvatar(@PathVariable Long userId) {
        try {
            boolean result = userProfileService.deleteUserAvatar(userId);
            return Result.success("头像删除成功", result);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("头像删除失败，请稍后重试");
        }
    }
}
