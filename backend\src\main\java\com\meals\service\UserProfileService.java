package com.meals.service;

import com.meals.dto.UserProfileDTO;
import com.meals.dto.UserHealthStatusDTO;
import com.meals.dto.UserDietPreferenceDTO;
import com.meals.dto.UserNutritionGoalsDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户档案业务逻辑接口
 * 定义用户档案相关的业务操作方法
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface UserProfileService {

    /**
     * 获取用户完整档案信息
     * 
     * @param userId 用户ID
     * @return 用户完整档案信息
     * @throws RuntimeException 当用户不存在时抛出
     */
    UserProfileDTO getUserProfile(Long userId);

    /**
     * 更新用户基础信息
     * 
     * @param userId 用户ID
     * @param profileDTO 用户档案信息
     * @return 更新后的用户档案信息
     * @throws RuntimeException 当用户不存在或更新失败时抛出
     */
    UserProfileDTO updateUserBasicInfo(Long userId, UserProfileDTO profileDTO);

    /**
     * 更新用户健康状况
     * 
     * @param userId 用户ID
     * @param healthStatusDTO 健康状况信息
     * @return 更新后的健康状况信息
     * @throws RuntimeException 当用户不存在或更新失败时抛出
     */
    UserHealthStatusDTO updateUserHealthStatus(Long userId, UserHealthStatusDTO healthStatusDTO);

    /**
     * 更新用户饮食偏好
     * 
     * @param userId 用户ID
     * @param dietPreferenceDTO 饮食偏好信息
     * @return 更新后的饮食偏好信息
     * @throws RuntimeException 当用户不存在或更新失败时抛出
     */
    UserDietPreferenceDTO updateUserDietPreference(Long userId, UserDietPreferenceDTO dietPreferenceDTO);

    /**
     * 更新用户营养目标
     * 
     * @param userId 用户ID
     * @param nutritionGoalsDTO 营养目标信息
     * @return 更新后的营养目标信息
     * @throws RuntimeException 当用户不存在或更新失败时抛出
     */
    UserNutritionGoalsDTO updateUserNutritionGoals(Long userId, UserNutritionGoalsDTO nutritionGoalsDTO);

    /**
     * 上传用户头像
     * 
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     * @throws RuntimeException 当用户不存在、文件格式不支持或上传失败时抛出
     */
    String uploadUserAvatar(Long userId, MultipartFile file);

    /**
     * 删除用户头像
     * 
     * @param userId 用户ID
     * @return 操作结果
     * @throws RuntimeException 当用户不存在或删除失败时抛出
     */
    boolean deleteUserAvatar(Long userId);
}
