<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <router-link to="/login" class="nav-btn login-btn">登录</router-link>
          <router-link to="/register" class="nav-btn register-btn">注册</router-link>
        </div>
      </div>
    </nav>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">科学饮食，智慧健康</h1>
        <p class="hero-subtitle">
          基于AI技术的个性化营养分析平台，为您提供专业的膳食指导和健康管理服务
        </p>
        <div class="hero-features">
          <div class="feature-tag">🥗 智能营养分析</div>
          <div class="feature-tag">📊 个性化建议</div>
          <div class="feature-tag">🎯 健康目标管理</div>
        </div>
        <div class="hero-actions">
          <router-link to="/register" class="cta-btn primary">立即开始</router-link>
          <button class="cta-btn secondary" @click="scrollToFeatures">了解更多</button>
        </div>
      </div>
    </section>

    <!-- 核心功能模块展示 -->
    <section class="features-section" ref="featuresSection">
      <div class="section-content">
        <div class="section-header">
          <h2>九大核心功能</h2>
          <p>全方位的营养健康管理服务，从基础记录到智能分析</p>
        </div>
        
        <div class="features-grid">
          <div 
            v-for="feature in features" 
            :key="feature.id"
            class="feature-card"
            @click="handleFeatureClick"
          >
            <div class="feature-icon" v-html="feature.icon"></div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-highlights">
              <span 
                v-for="highlight in feature.highlights" 
                :key="highlight"
                class="highlight-tag"
              >
                {{ highlight }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 平台优势 -->
    <section class="advantages-section">
      <div class="section-content">
        <div class="section-header">
          <h2>为什么选择我们</h2>
          <p>专业、智能、个性化的营养健康管理解决方案</p>
        </div>
        
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🔬</div>
            <h3>科学权威</h3>
            <p>基于中国居民膳食指南和国际营养标准，提供科学可靠的营养分析</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤖</div>
            <h3>智能分析</h3>
            <p>AI驱动的营养计算引擎，精确分析膳食营养成分和健康风险</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>个性定制</h3>
            <p>根据个人健康状况、饮食偏好和目标，提供量身定制的营养方案</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户见证 -->
    <section class="testimonials-section">
      <div class="section-content">
        <div class="section-header">
          <h2>用户成功案例</h2>
          <p>真实用户的健康改善之旅</p>
        </div>
        
        <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"通过平台的营养分析，我成功减重15公斤，血糖也控制得很好。"</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">👩</div>
              <div class="author-info">
                <span class="author-name">张女士</span>
                <span class="author-desc">糖尿病患者，减重成功</span>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"作为健身爱好者，这个平台帮我精确计算营养需求，增肌效果显著。"</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">👨</div>
              <div class="author-info">
                <span class="author-name">李先生</span>
                <span class="author-desc">健身教练，增肌成功</span>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"孕期营养管理变得简单，宝宝健康，我的体重也控制得很好。"</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">🤱</div>
              <div class="author-info">
                <span class="author-name">王女士</span>
                <span class="author-desc">准妈妈，孕期营养管理</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="footer-section">
      <div class="footer-content">
        <div class="footer-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="footer-info">
          <p>&copy; 2025 膳食营养分析平台. 专业的营养健康管理服务</p>
          <p>科学饮食，健康生活</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const featuresSection = ref<HTMLElement>()

// 核心功能数据
const features = ref([
  {
    id: 1,
    title: '用户管理与个人档案',
    description: '完善的用户认证系统和个人健康档案管理',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" 
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['健康档案', '饮食偏好', '营养目标']
  },
  {
    id: 2,
    title: '食物数据库管理',
    description: '综合食物数据库，包含营养成分信息和智能搜索功能',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['食物搜索', '营养成分', '智能分类']
  },
  {
    id: 3,
    title: '膳食记录与输入',
    description: '多种便捷的膳食记录方式，支持手动输入和智能识别',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['拍照识别', '语音输入', '快速记录']
  },
  {
    id: 4,
    title: '营养分析计算',
    description: '精确的营养素计算和膳食评估，基于科学算法',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9 11H15M9 15H15M17 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H12.586C12.8512 3.00006 13.1055 3.10545 13.293 3.293L18.707 8.707C18.8946 8.89449 18.9999 9.14881 19 9.414V19C19 19.5304 18.7893 20.0391 18.4142 20.4142C18.0391 20.7893 17.5304 21 17 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['精确计算', '膳食评估', '营养分析']
  },
  {
    id: 5,
    title: '智能推荐建议',
    description: '个性化膳食建议和食谱推荐，AI驱动的营养指导',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.663 17H4.5C3.837 17 3.201 16.737 2.732 16.268C2.263 15.799 2 15.163 2 14.5C2 13.837 2.263 13.201 2.732 12.732C3.201 12.263 3.837 12 4.5 12H9.663M9.663 17L7.5 19.5M9.663 17L7.5 14.5M14.337 7H19.5C20.163 7 20.799 7.263 21.268 7.732C21.737 8.201 22 8.837 22 9.5C22 10.163 21.737 10.799 21.268 11.268C20.799 11.737 20.163 12 19.5 12H14.337M14.337 7L16.5 4.5M14.337 7L16.5 9.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['个性推荐', '食谱建议', '营养补充']
  },
  {
    id: 6,
    title: '数据可视化报告',
    description: '直观的营养摄入图表和健康报告生成',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <line x1="18" y1="20" x2="18" y2="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="12" y1="20" x2="12" y2="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="6" y1="20" x2="6" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['图表展示', '趋势分析', '报告生成']
  },
  {
    id: 7,
    title: '健康监测预警',
    description: '营养预警机制和健康风险评估系统',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.29 3.86L1.82 18C1.64486 18.3024 1.55674 18.6453 1.56554 18.9928C1.57434 19.3402 1.67967 19.6781 1.86821 19.9715C2.05674 20.2648 2.32374 20.5016 2.63803 20.6558C2.95232 20.8101 3.30248 20.8766 3.65 20.85H20.35C20.6975 20.8766 21.0477 20.8101 21.362 20.6558C21.6763 20.5016 21.9433 20.2648 22.1318 19.9715C22.3203 19.6781 22.4257 19.3402 22.4345 18.9928C22.4433 18.6453 22.3551 18.3024 22.18 18L13.71 3.86C13.5317 3.56611 13.2807 3.32312 12.9812 3.15448C12.6817 2.98585 12.3438 2.89725 12 2.89725C11.6562 2.89725 11.3183 2.98585 11.0188 3.15448C10.7193 3.32312 10.4683 3.56611 10.29 3.86V3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="17" r="1" fill="currentColor"/>
    </svg>`,
    highlights: ['营养预警', '风险评估', '健康监控']
  },
  {
    id: 8,
    title: '社交社区功能',
    description: '用户交流平台和专业营养师支持服务',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M23 21V19C23 18.1332 22.7361 17.2863 22.2416 16.5555C21.7471 15.8246 21.0424 15.2426 20.2209 14.8778" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M16 3.13C16.8604 3.47863 17.5963 4.07813 18.1147 4.84677C18.6331 5.61541 18.9136 6.51894 18.9136 7.445C18.9136 8.37106 18.6331 9.27459 18.1147 10.0432C17.5963 10.8119 16.8604 11.4114 16 11.76" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['用户交流', '专家咨询', '激励机制']
  },
  {
    id: 9,
    title: '系统管理运维',
    description: '完善的后台管理和数据安全保障系统',
    icon: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    highlights: ['后台管理', 'API服务', '数据安全']
  }
])

// 滚动到功能区域
const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

// 处理功能卡片点击
const handleFeatureClick = () => {
  // 未登录用户引导到登录页面
  router.push('/login')
}

onMounted(() => {
  // 页面加载动画等初始化逻辑
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* 全局样式 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 顶部导航栏 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-btn {
  padding: 0.5rem 1.5rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.login-btn {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.login-btn:hover {
  background: var(--primary-color);
  color: white;
}

.register-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* 英雄区域 */
.hero-section {
  position: relative;
  z-index: 1;
  padding: 4rem 0 6rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(22, 160, 133, 0.1);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.9rem;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.cta-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  box-shadow: var(--shadow-light);
}

.cta-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.cta-btn.secondary {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-btn.secondary:hover {
  background: var(--primary-color);
  color: white;
}

/* 通用区域样式 */
.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 功能模块展示 */
.features-section {
  position: relative;
  z-index: 1;
  padding: 6rem 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.feature-icon {
  width: 60px;
  height: 60px;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-highlights {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.highlight-tag {
  background: var(--background-light);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
}

/* 平台优势 */
.advantages-section {
  position: relative;
  z-index: 1;
  padding: 6rem 0;
  background: var(--background-light);
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

.advantage-item {
  text-align: center;
  padding: 2rem;
}

.advantage-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.advantage-item h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.advantage-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 用户见证 */
.testimonials-section {
  position: relative;
  z-index: 1;
  padding: 6rem 0;
  background: white;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-style: italic;
  position: relative;
}

.testimonial-content p::before {
  content: '"';
  font-size: 2rem;
  color: var(--primary-color);
  position: absolute;
  left: -1rem;
  top: -0.5rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
}

.author-desc {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 底部信息 */
.footer-section {
  position: relative;
  z-index: 1;
  padding: 3rem 0;
  background: var(--text-primary);
  color: white;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-brand {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.footer-brand .brand-logo {
  color: var(--primary-light);
}

.footer-brand .brand-name {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.footer-info p {
  margin: 0.5rem 0;
  color: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  .nav-brand .brand-name {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
  }

  .section-content {
    padding: 0 1rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 2rem 0 4rem 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .feature-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .cta-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .features-section,
  .advantages-section,
  .testimonials-section {
    padding: 4rem 0;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }
}
</style>
