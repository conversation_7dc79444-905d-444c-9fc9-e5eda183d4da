-- =============================================
-- 膳食营养分析平台 - 用户表设计
-- 创建时间: 2025-08-05
-- 作者: Meals Team
-- 版本: 1.0.0
-- =============================================

-- 使用数据库
USE `meals`;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `user`;

-- 创建用户表
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键自增',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名，唯一',
    `password` VARCHAR(255) NOT NULL COMMENT '密码，加密存储（格式：盐值:加密密码）',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    `name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `birth_date` DATE DEFAULT NULL COMMENT '出生日期',
    `age` INT DEFAULT NULL COMMENT '用户年龄',
    `gender` TINYINT DEFAULT NULL COMMENT '用户性别：0-女，1-男',
    `height` DECIMAL(5,2) DEFAULT NULL COMMENT '身高（厘米）',
    `weight` DECIMAL(5,2) DEFAULT NULL COMMENT '体重（公斤）',
    `body_fat` DECIMAL(5,2) DEFAULT NULL COMMENT '体脂率（%）',
    `muscle_mass` DECIMAL(5,2) DEFAULT NULL COMMENT '肌肉量（公斤）',
    `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '用户状态：0-禁用，1-启用',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =============================================
-- 用户健康状况表
-- =============================================
DROP TABLE IF EXISTS `user_health_status`;
CREATE TABLE `user_health_status` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `chronic_diseases` JSON DEFAULT NULL COMMENT '慢性病史（JSON格式）',
    `allergies` JSON DEFAULT NULL COMMENT '过敏信息（JSON格式）',
    `medications` JSON DEFAULT NULL COMMENT '用药记录（JSON格式）',
    `surgeries` JSON DEFAULT NULL COMMENT '手术史（JSON格式）',
    `family_history` JSON DEFAULT NULL COMMENT '家族病史（JSON格式）',
    `medical_reports` JSON DEFAULT NULL COMMENT '体检报告（JSON格式）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    CONSTRAINT `fk_health_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户健康状况表';

-- =============================================
-- 用户饮食偏好表
-- =============================================
DROP TABLE IF EXISTS `user_diet_preference`;
CREATE TABLE `user_diet_preference` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `vegetarian_type` VARCHAR(20) DEFAULT 'none' COMMENT '素食类型：none-无限制，pescatarian-鱼素，vegetarian-蛋奶素，vegan-严格素食',
    `restrictions` JSON DEFAULT NULL COMMENT '饮食限制（JSON格式）',
    `allergies` JSON DEFAULT NULL COMMENT '食物过敏（JSON格式）',
    `preferences` JSON DEFAULT NULL COMMENT '口味偏好（JSON格式）',
    `forbidden_foods` JSON DEFAULT NULL COMMENT '禁忌食物（JSON格式）',
    `favorite_foods` JSON DEFAULT NULL COMMENT '偏好食物（JSON格式）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    CONSTRAINT `fk_diet_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户饮食偏好表';

-- =============================================
-- 用户营养目标表
-- =============================================
DROP TABLE IF EXISTS `user_nutrition_goals`;
CREATE TABLE `user_nutrition_goals` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `weight_goal` JSON DEFAULT NULL COMMENT '体重目标（JSON格式）',
    `nutrition_goals` JSON DEFAULT NULL COMMENT '营养目标（JSON格式）',
    `disease_management` JSON DEFAULT NULL COMMENT '疾病管理目标（JSON格式）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    CONSTRAINT `fk_goals_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户营养目标表';
