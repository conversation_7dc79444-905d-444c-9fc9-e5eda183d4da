package com.meals.dto;

import java.util.List;
import java.util.Map;

/**
 * 用户饮食偏好DTO
 * 用于前端和后端之间的饮食偏好数据传输
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserDietPreferenceDTO {

    /**
     * 素食类型：none-无限制，pescatarian-鱼素，vegetarian-蛋奶素，vegan-严格素食
     */
    private String vegetarianType;

    /**
     * 饮食限制列表
     */
    private List<String> restrictions;

    /**
     * 食物过敏列表
     */
    private List<Map<String, Object>> allergies;

    /**
     * 口味偏好设置
     */
    private Map<String, Object> preferences;

    /**
     * 禁忌食物列表
     */
    private List<String> forbiddenFoods;

    /**
     * 偏好食物列表
     */
    private List<String> favoriteFoods;

    // ==================== Getter和Setter方法 ====================

    public String getVegetarianType() {
        return vegetarianType;
    }

    public void setVegetarianType(String vegetarianType) {
        this.vegetarianType = vegetarianType;
    }

    public List<String> getRestrictions() {
        return restrictions;
    }

    public void setRestrictions(List<String> restrictions) {
        this.restrictions = restrictions;
    }

    public List<Map<String, Object>> getAllergies() {
        return allergies;
    }

    public void setAllergies(List<Map<String, Object>> allergies) {
        this.allergies = allergies;
    }

    public Map<String, Object> getPreferences() {
        return preferences;
    }

    public void setPreferences(Map<String, Object> preferences) {
        this.preferences = preferences;
    }

    public List<String> getForbiddenFoods() {
        return forbiddenFoods;
    }

    public void setForbiddenFoods(List<String> forbiddenFoods) {
        this.forbiddenFoods = forbiddenFoods;
    }

    public List<String> getFavoriteFoods() {
        return favoriteFoods;
    }

    public void setFavoriteFoods(List<String> favoriteFoods) {
        this.favoriteFoods = favoriteFoods;
    }
}
