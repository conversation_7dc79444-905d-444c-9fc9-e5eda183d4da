package com.meals.mapper;

import com.meals.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户数据访问层接口
 * 使用MyBatis进行数据库操作
 * SQL查询定义在对应的XML映射文件中
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserMapper {

    /**
     * 插入用户
     *
     * @param user 用户信息
     * @return 影响行数
     */
    int insert(User user);

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User selectById(@Param("id") Long id);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 影响行数
     */
    int updateById(User user);

    /**
     * 根据ID删除用户（逻辑删除）
     *
     * @param id 用户ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户名查询用户信息
     *
     * @param username 用户名
     * @return 用户信息，如果不存在返回null
     */
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱地址
     * @return 用户信息，如果不存在返回null
     */
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号码
     * @return 用户信息，如果不存在返回null
     */
    User findByPhone(@Param("phone") String phone);

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 存在返回true，不存在返回false
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱地址
     * @return 存在返回true，不存在返回false
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否已存在
     *
     * @param phone 手机号码
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 分页查询用户列表（支持条件查询）
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @param username 用户名（模糊查询，可为空）
     * @param email 邮箱（模糊查询，可为空）
     * @param status 用户状态（可为空）
     * @return 用户列表
     */
    List<User> selectUserPage(@Param("offset") int offset,
                             @Param("limit") int limit,
                             @Param("username") String username,
                             @Param("email") String email,
                             @Param("status") Integer status);

    /**
     * 查询活跃用户列表（状态为启用且未删除）
     *
     * @param limit 限制数量
     * @return 活跃用户列表
     */
    List<User> findActiveUsers(@Param("limit") Integer limit);

    /**
     * 根据多个条件查询用户
     *
     * @param username 用户名（可为空）
     * @param email 邮箱（可为空）
     * @param phone 手机号（可为空）
     * @param status 状态（可为空）
     * @return 用户列表
     */
    List<User> findUsersByConditions(@Param("username") String username,
                                   @Param("email") String email,
                                   @Param("phone") String phone,
                                   @Param("status") Integer status);

    /**
     * 统计用户数量（按条件）
     *
     * @param status 用户状态（可为空）
     * @return 用户数量
     */
    Long countUsersByStatus(@Param("status") Integer status);
}
