package com.meals.entity;

import java.time.LocalDateTime;

/**
 * 用户营养目标实体类
 * 对应数据库user_nutrition_goals表
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserNutritionGoals {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 体重目标（JSON格式）
     */
    private String weightGoal;

    /**
     * 营养目标（JSON格式）
     */
    private String nutritionGoals;

    /**
     * 疾病管理目标（JSON格式）
     */
    private String diseaseManagement;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // ==================== Getter和Setter方法 ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getWeightGoal() {
        return weightGoal;
    }

    public void setWeightGoal(String weightGoal) {
        this.weightGoal = weightGoal;
    }

    public String getNutritionGoals() {
        return nutritionGoals;
    }

    public void setNutritionGoals(String nutritionGoals) {
        this.nutritionGoals = nutritionGoals;
    }

    public String getDiseaseManagement() {
        return diseaseManagement;
    }

    public void setDiseaseManagement(String diseaseManagement) {
        this.diseaseManagement = diseaseManagement;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
