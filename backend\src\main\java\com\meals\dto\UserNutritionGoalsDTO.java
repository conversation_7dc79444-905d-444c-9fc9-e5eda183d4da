package com.meals.dto;

import java.util.Map;

/**
 * 用户营养目标DTO
 * 用于前端和后端之间的营养目标数据传输
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserNutritionGoalsDTO {

    /**
     * 体重目标设置
     */
    private Map<String, Object> weightGoal;

    /**
     * 营养目标设置
     */
    private Map<String, Object> nutritionGoals;

    /**
     * 疾病管理目标设置
     */
    private Map<String, Object> diseaseManagement;

    // ==================== Getter和Setter方法 ====================

    public Map<String, Object> getWeightGoal() {
        return weightGoal;
    }

    public void setWeightGoal(Map<String, Object> weightGoal) {
        this.weightGoal = weightGoal;
    }

    public Map<String, Object> getNutritionGoals() {
        return nutritionGoals;
    }

    public void setNutritionGoals(Map<String, Object> nutritionGoals) {
        this.nutritionGoals = nutritionGoals;
    }

    public Map<String, Object> getDiseaseManagement() {
        return diseaseManagement;
    }

    public void setDiseaseManagement(Map<String, Object> diseaseManagement) {
        this.diseaseManagement = diseaseManagement;
    }
}
