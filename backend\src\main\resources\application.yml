server:
  port: 8081
  servlet:
    context-path: /api

spring:
  application:
    name: meals

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: root
    password: "021026"

  # JPA配置 - 禁用open-in-view以消除警告
  jpa:
    open-in-view: false

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 5MB
      max-request-size: 10MB
      file-size-threshold: 2KB
    

# MyBatis配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.meals.entity

# 文件上传配置
file:
  upload:
    path: ./uploads
    domain: http://localhost:8081/api

# 日志配置
logging:
  level:
    com.meals: debug
    org.springframework.web: debug
    root: info
  pattern:
    # 彩色控制台日志格式
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){magenta} %clr(%-5level){highlight} %clr(%logger{36}){cyan} %clr(-){faint} %clr(%msg){green}%n"
