<template>
  <div class="basic-info-tab">
    <div class="tab-header">
      <h2>基础信息管理</h2>
      <p>管理您的个人基本信息和身体数据</p>
    </div>

    <div class="info-sections">
      <!-- 个人基本信息 -->
      <div class="info-section">
        <h3>个人信息</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>姓名</label>
            <input 
              v-model="localUserInfo.name" 
              type="text" 
              placeholder="请输入您的姓名"
              @input="handleUpdate"
            />
          </div>
          
          <div class="form-group">
            <label>性别</label>
            <select v-model="localUserInfo.gender" @change="handleUpdate">
              <option value="">请选择</option>
              <option value="male">男</option>
              <option value="female">女</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>出生日期</label>
            <input 
              v-model="localUserInfo.birthDate" 
              type="date" 
              @change="handleUpdate"
            />
          </div>
          
          <div class="form-group">
            <label>年龄</label>
            <input 
              :value="calculatedAge" 
              type="text" 
              readonly 
              placeholder="根据出生日期自动计算"
            />
          </div>
        </div>
      </div>

      <!-- 身体数据 -->
      <div class="info-section">
        <h3>身体数据</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>身高 (cm)</label>
            <input 
              v-model.number="localUserInfo.height" 
              type="number" 
              placeholder="请输入身高"
              @input="handleUpdate"
            />
          </div>
          
          <div class="form-group">
            <label>体重 (kg)</label>
            <input 
              v-model.number="localUserInfo.weight" 
              type="number" 
              step="0.1"
              placeholder="请输入体重"
              @input="handleUpdate"
            />
          </div>
          
          <div class="form-group">
            <label>体脂率 (%)</label>
            <input 
              v-model.number="localUserInfo.bodyFat" 
              type="number" 
              step="0.1"
              placeholder="请输入体脂率"
              @input="handleUpdate"
            />
          </div>
          
          <div class="form-group">
            <label>肌肉量 (kg)</label>
            <input 
              v-model.number="localUserInfo.muscleMass" 
              type="number" 
              step="0.1"
              placeholder="请输入肌肉量"
              @input="handleUpdate"
            />
          </div>
        </div>
      </div>

      <!-- BMI分析 -->
      <div class="info-section">
        <h3>BMI分析</h3>
        <div class="bmi-analysis">
          <div class="bmi-display">
            <div class="bmi-value">
              <span class="bmi-number">{{ bmiValue }}</span>
              <span class="bmi-unit">BMI</span>
            </div>
            <div class="bmi-status" :class="bmiStatusClass">
              {{ bmiStatus }}
            </div>
          </div>
          
          <div class="bmi-chart">
            <div class="bmi-scale">
              <div class="scale-item underweight">
                <span>偏瘦</span>
                <span>&lt;18.5</span>
              </div>
              <div class="scale-item normal">
                <span>正常</span>
                <span>18.5-23.9</span>
              </div>
              <div class="scale-item overweight">
                <span>偏胖</span>
                <span>24.0-27.9</span>
              </div>
              <div class="scale-item obese">
                <span>肥胖</span>
                <span>≥28.0</span>
              </div>
            </div>
            <div class="bmi-indicator" :style="{ left: bmiIndicatorPosition }"></div>
          </div>
        </div>
      </div>

      <!-- 头像管理 -->
      <div class="info-section">
        <h3>头像管理</h3>
        <div class="avatar-section">
          <div class="current-avatar">
            <img v-if="localUserInfo.avatar" :src="localUserInfo.avatar" alt="当前头像" />
            <div v-else class="avatar-placeholder">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" 
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          
          <div class="avatar-actions">
            <input 
              ref="fileInput" 
              type="file" 
              accept="image/*" 
              style="display: none"
              @change="handleAvatarUpload"
            />
            <button @click="$refs.fileInput.click()" class="upload-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" 
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="7,10 12,5 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="12" y1="5" x2="12" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              上传头像
            </button>
            <button v-if="localUserInfo.avatar" @click="removeAvatar" class="remove-btn">
              删除头像
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps<{
  userInfo: any
}>()

// Emits
const emit = defineEmits<{
  update: []
}>()

// 本地数据
const localUserInfo = ref({ ...props.userInfo })
const fileInput = ref<HTMLInputElement>()

// 监听props变化
watch(() => props.userInfo, (newVal) => {
  localUserInfo.value = { ...newVal }
}, { deep: true })

// 计算年龄
const calculatedAge = computed(() => {
  if (!localUserInfo.value.birthDate) return ''
  const today = new Date()
  const birthDate = new Date(localUserInfo.value.birthDate)
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  localUserInfo.value.age = age
  return age.toString()
})

// 计算BMI
const bmiValue = computed(() => {
  if (!localUserInfo.value.height || !localUserInfo.value.weight) return '--'
  const heightInM = localUserInfo.value.height / 100
  const bmi = localUserInfo.value.weight / (heightInM * heightInM)
  return bmi.toFixed(1)
})

// BMI状态
const bmiStatus = computed(() => {
  const bmi = parseFloat(bmiValue.value)
  if (isNaN(bmi)) return '请输入身高体重'
  if (bmi < 18.5) return '偏瘦'
  if (bmi < 24) return '正常'
  if (bmi < 28) return '偏胖'
  return '肥胖'
})

// BMI状态样式类
const bmiStatusClass = computed(() => {
  const bmi = parseFloat(bmiValue.value)
  if (isNaN(bmi)) return ''
  if (bmi < 18.5) return 'underweight'
  if (bmi < 24) return 'normal'
  if (bmi < 28) return 'overweight'
  return 'obese'
})

// BMI指示器位置
const bmiIndicatorPosition = computed(() => {
  const bmi = parseFloat(bmiValue.value)
  if (isNaN(bmi)) return '0%'
  
  let position = 0
  if (bmi < 18.5) {
    position = (bmi / 18.5) * 25
  } else if (bmi < 24) {
    position = 25 + ((bmi - 18.5) / (24 - 18.5)) * 25
  } else if (bmi < 28) {
    position = 50 + ((bmi - 24) / (28 - 24)) * 25
  } else {
    position = 75 + Math.min(((bmi - 28) / 10) * 25, 25)
  }
  
  return Math.min(position, 100) + '%'
})

// 处理数据更新
const handleUpdate = () => {
  emit('update')
}

// 处理头像上传
const handleAvatarUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      localUserInfo.value.avatar = e.target?.result as string
      handleUpdate()
    }
    reader.readAsDataURL(file)
  }
}

// 删除头像
const removeAvatar = () => {
  localUserInfo.value.avatar = ''
  handleUpdate()
}

// 监听本地数据变化，同步到父组件
watch(localUserInfo, (newVal) => {
  Object.assign(props.userInfo, newVal)
}, { deep: true })
</script>

<style scoped>
.basic-info-tab {
  max-width: 800px;
}

.tab-header {
  margin-bottom: 2rem;
}

.tab-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.tab-header p {
  margin: 0;
  color: #64748b;
}

.info-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.info-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

/* BMI分析样式 */
.bmi-analysis {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  align-items: center;
}

.bmi-display {
  text-align: center;
}

.bmi-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.bmi-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2d3748;
}

.bmi-unit {
  font-size: 0.9rem;
  color: #64748b;
}

.bmi-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
}

.bmi-status.underweight {
  background: #dbeafe;
  color: #1e40af;
}

.bmi-status.normal {
  background: #dcfce7;
  color: #166534;
}

.bmi-status.overweight {
  background: #fef3c7;
  color: #92400e;
}

.bmi-status.obese {
  background: #fee2e2;
  color: #dc2626;
}

.bmi-chart {
  position: relative;
}

.bmi-scale {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.scale-item {
  padding: 1rem;
  text-align: center;
  color: white;
  font-size: 0.8rem;
}

.scale-item span {
  display: block;
}

.scale-item.underweight {
  background: #3b82f6;
}

.scale-item.normal {
  background: #10b981;
}

.scale-item.overweight {
  background: #f59e0b;
}

.scale-item.obese {
  background: #ef4444;
}

.bmi-indicator {
  position: absolute;
  top: -10px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 12px solid #2d3748;
  transform: translateX(-50%);
  transition: left 0.3s ease;
}

/* 头像管理样式 */
.avatar-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.current-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.current-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  color: #64748b;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.upload-btn,
.remove-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-btn {
  background: #16a085;
  color: white;
}

.upload-btn:hover {
  background: #138d75;
}

.upload-btn svg {
  width: 16px;
  height: 16px;
}

.remove-btn {
  background: #ef4444;
  color: white;
}

.remove-btn:hover {
  background: #dc2626;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .bmi-analysis {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .avatar-section {
    flex-direction: column;
    align-items: center;
  }
}
</style>
