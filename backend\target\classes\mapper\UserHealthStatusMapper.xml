<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meals.mapper.UserHealthStatusMapper">

    <!-- 用户健康状况结果映射 -->
    <resultMap id="BaseResultMap" type="UserHealthStatus">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="chronic_diseases" property="chronicDiseases" jdbcType="VARCHAR"/>
        <result column="allergies" property="allergies" jdbcType="VARCHAR"/>
        <result column="medications" property="medications" jdbcType="VARCHAR"/>
        <result column="surgeries" property="surgeries" jdbcType="VARCHAR"/>
        <result column="family_history" property="familyHistory" jdbcType="VARCHAR"/>
        <result column="medical_reports" property="medicalReports" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, chronic_diseases, allergies, medications, surgeries, 
        family_history, medical_reports, create_time, update_time
    </sql>

    <!-- 插入用户健康状况 -->
    <insert id="insert" parameterType="UserHealthStatus" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_health_status (user_id, chronic_diseases, allergies, medications, surgeries, family_history, medical_reports, create_time, update_time)
        VALUES (#{userId}, #{chronicDiseases}, #{allergies}, #{medications}, #{surgeries}, #{familyHistory}, #{medicalReports}, NOW(), NOW())
    </insert>

    <!-- 根据用户ID查询健康状况 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_health_status
        WHERE user_id = #{userId}
    </select>

    <!-- 更新用户健康状况 -->
    <update id="updateByUserId" parameterType="UserHealthStatus">
        UPDATE user_health_status
        SET
            chronic_diseases = #{chronicDiseases},
            allergies = #{allergies},
            medications = #{medications},
            surgeries = #{surgeries},
            family_history = #{familyHistory},
            medical_reports = #{medicalReports},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 根据用户ID删除健康状况记录 -->
    <delete id="deleteByUserId">
        DELETE FROM user_health_status
        WHERE user_id = #{userId}
    </delete>

</mapper>
