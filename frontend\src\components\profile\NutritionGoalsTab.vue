<template>
  <div class="nutrition-goals-tab">
    <div class="tab-header">
      <h2>营养目标管理</h2>
      <p>设置和管理您的体重、营养和健康目标</p>
    </div>

    <div class="goals-sections">
      <!-- 体重管理目标 -->
      <div class="goals-section">
        <h3>体重管理目标</h3>
        
        <div class="weight-goal-setup">
          <div class="current-stats">
            <div class="stat-card">
              <span class="stat-label">当前体重</span>
              <span class="stat-value">{{ currentWeight || '--' }} kg</span>
            </div>
            <div class="stat-card">
              <span class="stat-label">当前BMI</span>
              <span class="stat-value">{{ currentBMI || '--' }}</span>
            </div>
            <div class="stat-card">
              <span class="stat-label">目标体重</span>
              <span class="stat-value">{{ localGoalsData.weightGoal.target || '--' }} kg</span>
            </div>
          </div>

          <div class="goal-form">
            <div class="form-row">
              <div class="form-group">
                <label>目标类型</label>
                <select v-model="localGoalsData.weightGoal.type" @change="handleUpdate">
                  <option value="">请选择</option>
                  <option value="lose">减重</option>
                  <option value="gain">增重</option>
                  <option value="maintain">维持</option>
                </select>
              </div>
              
              <div class="form-group">
                <label>目标体重 (kg)</label>
                <input 
                  v-model.number="localGoalsData.weightGoal.target" 
                  type="number" 
                  step="0.1"
                  placeholder="目标体重"
                  @input="handleUpdate"
                />
              </div>
              
              <div class="form-group">
                <label>目标日期</label>
                <input 
                  v-model="localGoalsData.weightGoal.deadline" 
                  type="date" 
                  @change="handleUpdate"
                />
              </div>
            </div>

            <div class="weekly-goal">
              <label>每周目标变化 (kg)</label>
              <div class="goal-options">
                <label 
                  v-for="option in weeklyGoalOptions" 
                  :key="option.value"
                  class="goal-option"
                >
                  <input 
                    type="radio" 
                    :value="option.value" 
                    v-model="localGoalsData.weightGoal.weeklyChange"
                    @change="handleUpdate"
                  />
                  <span>{{ option.label }}</span>
                  <span class="option-desc">{{ option.description }}</span>
                </label>
              </div>
            </div>

            <div class="progress-tracking" v-if="localGoalsData.weightGoal.target">
              <h4>进度追踪</h4>
              <div class="progress-info">
                <div class="progress-stat">
                  <span>需要变化: {{ weightChangeNeeded }} kg</span>
                </div>
                <div class="progress-stat">
                  <span>预计时间: {{ estimatedWeeks }} 周</span>
                </div>
                <div class="progress-stat">
                  <span>每日热量调整: {{ dailyCalorieAdjustment }} kcal</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 特定营养素目标 -->
      <div class="goals-section">
        <h3>特定营养素目标</h3>
        
        <div class="nutrition-goals">
          <div class="nutrition-category">
            <h4>宏量营养素</h4>
            <div class="macro-goals">
              <div 
                v-for="macro in macroNutrients" 
                :key="macro.key"
                class="macro-goal"
              >
                <label>{{ macro.label }}</label>
                <div class="goal-input-group">
                  <input 
                    v-model.number="localGoalsData.nutritionGoals[macro.key]" 
                    type="number" 
                    :placeholder="macro.placeholder"
                    @input="handleUpdate"
                  />
                  <span class="unit">{{ macro.unit }}</span>
                </div>
                <div class="goal-note">{{ macro.note }}</div>
              </div>
            </div>
          </div>

          <div class="nutrition-category">
            <h4>维生素补充</h4>
            <div class="vitamin-goals">
              <div 
                v-for="vitamin in vitamins" 
                :key="vitamin.key"
                class="vitamin-goal"
              >
                <div class="vitamin-header">
                  <label>{{ vitamin.label }}</label>
                  <input 
                    type="checkbox" 
                    :checked="localGoalsData.nutritionGoals.vitamins?.[vitamin.key]?.enabled"
                    @change="toggleVitamin(vitamin.key, $event.target.checked)"
                  />
                </div>
                <div 
                  v-if="localGoalsData.nutritionGoals.vitamins?.[vitamin.key]?.enabled"
                  class="vitamin-details"
                >
                  <input 
                    v-model.number="localGoalsData.nutritionGoals.vitamins[vitamin.key].amount" 
                    type="number" 
                    :placeholder="vitamin.placeholder"
                    @input="handleUpdate"
                  />
                  <span class="unit">{{ vitamin.unit }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="nutrition-category">
            <h4>其他营养目标</h4>
            <div class="other-goals">
              <div class="goal-item">
                <label>膳食纤维 (g/天)</label>
                <input 
                  v-model.number="localGoalsData.nutritionGoals.fiber" 
                  type="number" 
                  placeholder="25-35"
                  @input="handleUpdate"
                />
              </div>
              <div class="goal-item">
                <label>水分摄入 (L/天)</label>
                <input 
                  v-model.number="localGoalsData.nutritionGoals.water" 
                  type="number" 
                  step="0.1"
                  placeholder="2.0-3.0"
                  @input="handleUpdate"
                />
              </div>
              <div class="goal-item">
                <label>钠摄入限制 (mg/天)</label>
                <input 
                  v-model.number="localGoalsData.nutritionGoals.sodium" 
                  type="number" 
                  placeholder="< 2300"
                  @input="handleUpdate"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 疾病管理目标 -->
      <div class="goals-section">
        <h3>疾病管理目标</h3>
        
        <div class="disease-management">
          <div class="disease-category">
            <h4>糖尿病管理</h4>
            <div class="disease-goals">
              <div class="goal-toggle">
                <label>
                  <input 
                    type="checkbox" 
                    v-model="localGoalsData.diseaseManagement.diabetes.enabled"
                    @change="handleUpdate"
                  />
                  启用糖尿病饮食管理
                </label>
              </div>
              <div 
                v-if="localGoalsData.diseaseManagement.diabetes.enabled"
                class="disease-details"
              >
                <div class="goal-item">
                  <label>目标血糖范围 (mmol/L)</label>
                  <div class="range-inputs">
                    <input 
                      v-model.number="localGoalsData.diseaseManagement.diabetes.bloodSugarMin" 
                      type="number" 
                      step="0.1"
                      placeholder="最低值"
                      @input="handleUpdate"
                    />
                    <span>-</span>
                    <input 
                      v-model.number="localGoalsData.diseaseManagement.diabetes.bloodSugarMax" 
                      type="number" 
                      step="0.1"
                      placeholder="最高值"
                      @input="handleUpdate"
                    />
                  </div>
                </div>
                <div class="goal-item">
                  <label>每日碳水化合物限制 (g)</label>
                  <input 
                    v-model.number="localGoalsData.diseaseManagement.diabetes.carbLimit" 
                    type="number" 
                    placeholder="150-200"
                    @input="handleUpdate"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="disease-category">
            <h4>高血压管理</h4>
            <div class="disease-goals">
              <div class="goal-toggle">
                <label>
                  <input 
                    type="checkbox" 
                    v-model="localGoalsData.diseaseManagement.hypertension.enabled"
                    @change="handleUpdate"
                  />
                  启用高血压饮食管理
                </label>
              </div>
              <div 
                v-if="localGoalsData.diseaseManagement.hypertension.enabled"
                class="disease-details"
              >
                <div class="goal-item">
                  <label>目标血压 (mmHg)</label>
                  <div class="range-inputs">
                    <input 
                      v-model.number="localGoalsData.diseaseManagement.hypertension.systolicTarget" 
                      type="number" 
                      placeholder="收缩压"
                      @input="handleUpdate"
                    />
                    <span>/</span>
                    <input 
                      v-model.number="localGoalsData.diseaseManagement.hypertension.diastolicTarget" 
                      type="number" 
                      placeholder="舒张压"
                      @input="handleUpdate"
                    />
                  </div>
                </div>
                <div class="goal-item">
                  <label>每日钠摄入限制 (mg)</label>
                  <input 
                    v-model.number="localGoalsData.diseaseManagement.hypertension.sodiumLimit" 
                    type="number" 
                    placeholder="< 1500"
                    @input="handleUpdate"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="disease-category">
            <h4>高血脂管理</h4>
            <div class="disease-goals">
              <div class="goal-toggle">
                <label>
                  <input 
                    type="checkbox" 
                    v-model="localGoalsData.diseaseManagement.hyperlipidemia.enabled"
                    @change="handleUpdate"
                  />
                  启用高血脂饮食管理
                </label>
              </div>
              <div 
                v-if="localGoalsData.diseaseManagement.hyperlipidemia.enabled"
                class="disease-details"
              >
                <div class="goal-item">
                  <label>每日胆固醇限制 (mg)</label>
                  <input 
                    v-model.number="localGoalsData.diseaseManagement.hyperlipidemia.cholesterolLimit" 
                    type="number" 
                    placeholder="< 300"
                    @input="handleUpdate"
                  />
                </div>
                <div class="goal-item">
                  <label>饱和脂肪限制 (%总热量)</label>
                  <input 
                    v-model.number="localGoalsData.diseaseManagement.hyperlipidemia.saturatedFatLimit" 
                    type="number" 
                    placeholder="< 10"
                    @input="handleUpdate"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps<{
  goalsData: any,
  userInfo?: any
}>()

// Emits
const emit = defineEmits<{
  update: []
}>()

// 本地数据
const localGoalsData = ref({ ...props.goalsData })

// 每周目标选项
const weeklyGoalOptions = [
  { value: 0.25, label: '0.25 kg', description: '缓慢稳定' },
  { value: 0.5, label: '0.5 kg', description: '适中健康' },
  { value: 0.75, label: '0.75 kg', description: '较快进度' },
  { value: 1.0, label: '1.0 kg', description: '快速但需谨慎' }
]

// 宏量营养素
const macroNutrients = [
  { key: 'protein', label: '蛋白质', unit: 'g/天', placeholder: '体重×1.2-2.0', note: '增肌期可适当增加' },
  { key: 'carbs', label: '碳水化合物', unit: 'g/天', placeholder: '总热量45-65%', note: '主要能量来源' },
  { key: 'fat', label: '脂肪', unit: 'g/天', placeholder: '总热量20-35%', note: '必需脂肪酸来源' },
  { key: 'calories', label: '总热量', unit: 'kcal/天', placeholder: '根据目标计算', note: '基础代谢+活动消耗' }
]

// 维生素
const vitamins = [
  { key: 'vitaminD', label: '维生素D', unit: 'IU/天', placeholder: '600-800' },
  { key: 'vitaminB12', label: '维生素B12', unit: 'μg/天', placeholder: '2.4' },
  { key: 'vitaminC', label: '维生素C', unit: 'mg/天', placeholder: '75-90' },
  { key: 'calcium', label: '钙', unit: 'mg/天', placeholder: '1000-1200' },
  { key: 'iron', label: '铁', unit: 'mg/天', placeholder: '8-18' },
  { key: 'omega3', label: 'Omega-3', unit: 'g/天', placeholder: '1.1-1.6' }
]

// 计算属性
const currentWeight = computed(() => props.userInfo?.weight)
const currentBMI = computed(() => {
  if (!props.userInfo?.height || !props.userInfo?.weight) return null
  const heightInM = props.userInfo.height / 100
  const bmi = props.userInfo.weight / (heightInM * heightInM)
  return bmi.toFixed(1)
})

const weightChangeNeeded = computed(() => {
  if (!currentWeight.value || !localGoalsData.value.weightGoal.target) return 0
  return Math.abs(localGoalsData.value.weightGoal.target - currentWeight.value).toFixed(1)
})

const estimatedWeeks = computed(() => {
  if (!localGoalsData.value.weightGoal.weeklyChange || !weightChangeNeeded.value) return 0
  return Math.ceil(parseFloat(weightChangeNeeded.value) / localGoalsData.value.weightGoal.weeklyChange)
})

const dailyCalorieAdjustment = computed(() => {
  if (!localGoalsData.value.weightGoal.weeklyChange) return 0
  // 1kg脂肪 ≈ 7700kcal
  const weeklyCalorieChange = localGoalsData.value.weightGoal.weeklyChange * 7700
  const dailyChange = weeklyCalorieChange / 7
  
  if (localGoalsData.value.weightGoal.type === 'lose') {
    return -Math.round(dailyChange)
  } else if (localGoalsData.value.weightGoal.type === 'gain') {
    return Math.round(dailyChange)
  }
  return 0
})

// 监听props变化
watch(() => props.goalsData, (newVal) => {
  localGoalsData.value = { ...newVal }
}, { deep: true })

// 切换维生素目标
const toggleVitamin = (vitaminKey: string, enabled: boolean) => {
  if (!localGoalsData.value.nutritionGoals.vitamins) {
    localGoalsData.value.nutritionGoals.vitamins = {}
  }
  
  if (enabled) {
    localGoalsData.value.nutritionGoals.vitamins[vitaminKey] = {
      enabled: true,
      amount: null
    }
  } else {
    delete localGoalsData.value.nutritionGoals.vitamins[vitaminKey]
  }
  
  handleUpdate()
}

// 处理数据更新
const handleUpdate = () => {
  emit('update')
}

// 监听本地数据变化，同步到父组件
watch(localGoalsData, (newVal) => {
  Object.assign(props.goalsData, newVal)
}, { deep: true })

// 初始化数据结构
if (!localGoalsData.value.weightGoal) localGoalsData.value.weightGoal = {}
if (!localGoalsData.value.nutritionGoals) localGoalsData.value.nutritionGoals = {}
if (!localGoalsData.value.diseaseManagement) {
  localGoalsData.value.diseaseManagement = {
    diabetes: { enabled: false },
    hypertension: { enabled: false },
    hyperlipidemia: { enabled: false }
  }
}
</script>

<style scoped>
.nutrition-goals-tab {
  max-width: 900px;
}

.tab-header {
  margin-bottom: 2rem;
}

.tab-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.tab-header p {
  margin: 0;
  color: #64748b;
}

.goals-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.goals-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.goals-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.goals-section h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
}

/* 体重目标样式 */
.current-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2d3748;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
}

.weekly-goal {
  margin-bottom: 2rem;
}

.weekly-goal label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 1rem;
}

.goal-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.goal-option {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.goal-option:hover {
  border-color: #16a085;
}

.goal-option input[type="radio"] {
  margin-bottom: 0.5rem;
}

.option-desc {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.progress-tracking {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.progress-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.progress-stat {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  font-weight: 500;
  color: #2d3748;
}

/* 营养目标样式 */
.nutrition-goals {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.macro-goals {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.macro-goal {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.macro-goal label {
  font-weight: 500;
  color: #374151;
}

.goal-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.goal-input-group input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

.unit {
  font-size: 0.9rem;
  color: #64748b;
  min-width: 60px;
}

.goal-note {
  font-size: 0.8rem;
  color: #64748b;
  font-style: italic;
}

.vitamin-goals {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.vitamin-goal {
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.vitamin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.vitamin-header label {
  font-weight: 500;
  color: #374151;
}

.vitamin-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vitamin-details input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.other-goals {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.goal-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.goal-item label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.goal-item input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

/* 疾病管理样式 */
.disease-management {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.disease-goals {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.goal-toggle {
  margin-bottom: 1rem;
}

.goal-toggle label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.disease-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.range-inputs input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

.range-inputs span {
  color: #64748b;
  font-weight: 500;
}

@media (max-width: 768px) {
  .current-stats,
  .form-row,
  .goal-options,
  .macro-goals,
  .vitamin-goals,
  .other-goals {
    grid-template-columns: 1fr;
  }
  
  .progress-info {
    grid-template-columns: 1fr;
  }
}
</style>
