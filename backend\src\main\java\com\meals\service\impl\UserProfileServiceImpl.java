package com.meals.service.impl;

import com.meals.dto.*;
import com.meals.entity.*;
import com.meals.mapper.*;
import com.meals.service.UserProfileService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 用户档案业务逻辑实现类
 * 实现用户档案相关的具体业务逻辑
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Transactional
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserHealthStatusMapper healthStatusMapper;

    @Autowired
    private UserDietPreferenceMapper dietPreferenceMapper;

    @Autowired
    private UserNutritionGoalsMapper nutritionGoalsMapper;

    @Value("${file.upload.path:/uploads}")
    private String uploadPath;

    @Value("${file.upload.domain:http://localhost:8080}")
    private String uploadDomain;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 支持的图片格式
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 获取用户完整档案信息
     */
    @Override
    @Transactional(readOnly = true)
    public UserProfileDTO getUserProfile(Long userId) {
        // 获取用户基础信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        UserProfileDTO profileDTO = new UserProfileDTO();
        // 复制基础信息
        profileDTO.setId(user.getId());
        profileDTO.setUsername(user.getUsername());
        profileDTO.setEmail(user.getEmail());
        profileDTO.setPhone(user.getPhone());
        profileDTO.setName(user.getName());
        profileDTO.setBirthDate(user.getBirthDate());
        profileDTO.setAge(user.getAge());
        profileDTO.setGender(user.getGender());
        profileDTO.setHeight(user.getHeight());
        profileDTO.setWeight(user.getWeight());
        profileDTO.setBodyFat(user.getBodyFat());
        profileDTO.setMuscleMass(user.getMuscleMass());
        profileDTO.setAvatar(user.getAvatar());

        // 获取健康状况信息
        UserHealthStatus healthStatus = healthStatusMapper.selectByUserId(userId);
        if (healthStatus != null) {
            profileDTO.setHealthData(convertToHealthStatusDTO(healthStatus));
        }

        // 获取饮食偏好信息
        UserDietPreference dietPreference = dietPreferenceMapper.selectByUserId(userId);
        if (dietPreference != null) {
            profileDTO.setDietData(convertToDietPreferenceDTO(dietPreference));
        }

        // 获取营养目标信息
        UserNutritionGoals nutritionGoals = nutritionGoalsMapper.selectByUserId(userId);
        if (nutritionGoals != null) {
            profileDTO.setGoalsData(convertToNutritionGoalsDTO(nutritionGoals));
        }

        return profileDTO;
    }

    /**
     * 更新用户基础信息
     */
    @Override
    public UserProfileDTO updateUserBasicInfo(Long userId, UserProfileDTO profileDTO) {
        // 检查用户是否存在
        User existingUser = userMapper.selectById(userId);
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }

        // 更新用户基础信息
        existingUser.setName(profileDTO.getName());
        existingUser.setBirthDate(profileDTO.getBirthDate());
        existingUser.setAge(profileDTO.getAge());
        existingUser.setGender(profileDTO.getGender());
        existingUser.setHeight(profileDTO.getHeight());
        existingUser.setWeight(profileDTO.getWeight());
        existingUser.setBodyFat(profileDTO.getBodyFat());
        existingUser.setMuscleMass(profileDTO.getMuscleMass());
        existingUser.setUpdateTime(LocalDateTime.now());

        int result = userMapper.updateById(existingUser);
        if (result <= 0) {
            throw new RuntimeException("更新用户信息失败");
        }

        return getUserProfile(userId);
    }

    /**
     * 更新用户健康状况
     */
    @Override
    public UserHealthStatusDTO updateUserHealthStatus(Long userId, UserHealthStatusDTO healthStatusDTO) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 查找现有健康状况记录
        UserHealthStatus existingHealthStatus = healthStatusMapper.selectByUserId(userId);

        if (existingHealthStatus == null) {
            // 创建新记录
            UserHealthStatus newHealthStatus = new UserHealthStatus();
            newHealthStatus.setUserId(userId);
            newHealthStatus.setChronicDiseases(convertToJsonString(healthStatusDTO.getChronicDiseases()));
            newHealthStatus.setAllergies(convertToJsonString(healthStatusDTO.getAllergies()));
            newHealthStatus.setMedications(convertToJsonString(healthStatusDTO.getMedications()));
            newHealthStatus.setSurgeries(convertToJsonString(healthStatusDTO.getSurgeries()));
            newHealthStatus.setFamilyHistory(convertToJsonString(healthStatusDTO.getFamilyHistory()));
            newHealthStatus.setMedicalReports(convertToJsonString(healthStatusDTO.getMedicalReports()));
            newHealthStatus.setCreateTime(LocalDateTime.now());
            newHealthStatus.setUpdateTime(LocalDateTime.now());

            int result = healthStatusMapper.insert(newHealthStatus);
            if (result <= 0) {
                throw new RuntimeException("创建健康状况记录失败");
            }
        } else {
            // 更新现有记录
            existingHealthStatus.setChronicDiseases(convertToJsonString(healthStatusDTO.getChronicDiseases()));
            existingHealthStatus.setAllergies(convertToJsonString(healthStatusDTO.getAllergies()));
            existingHealthStatus.setMedications(convertToJsonString(healthStatusDTO.getMedications()));
            existingHealthStatus.setSurgeries(convertToJsonString(healthStatusDTO.getSurgeries()));
            existingHealthStatus.setFamilyHistory(convertToJsonString(healthStatusDTO.getFamilyHistory()));
            existingHealthStatus.setMedicalReports(convertToJsonString(healthStatusDTO.getMedicalReports()));
            existingHealthStatus.setUpdateTime(LocalDateTime.now());

            int result = healthStatusMapper.updateByUserId(existingHealthStatus);
            if (result <= 0) {
                throw new RuntimeException("更新健康状况记录失败");
            }
        }

        return healthStatusDTO;
    }

    /**
     * 更新用户饮食偏好
     */
    @Override
    public UserDietPreferenceDTO updateUserDietPreference(Long userId, UserDietPreferenceDTO dietPreferenceDTO) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 查找现有饮食偏好记录
        UserDietPreference existingDietPreference = dietPreferenceMapper.selectByUserId(userId);

        if (existingDietPreference == null) {
            // 创建新记录
            UserDietPreference newDietPreference = new UserDietPreference();
            newDietPreference.setUserId(userId);
            newDietPreference.setVegetarianType(dietPreferenceDTO.getVegetarianType());
            newDietPreference.setRestrictions(convertToJsonString(dietPreferenceDTO.getRestrictions()));
            newDietPreference.setAllergies(convertToJsonString(dietPreferenceDTO.getAllergies()));
            newDietPreference.setPreferences(convertToJsonString(dietPreferenceDTO.getPreferences()));
            newDietPreference.setForbiddenFoods(convertToJsonString(dietPreferenceDTO.getForbiddenFoods()));
            newDietPreference.setFavoriteFoods(convertToJsonString(dietPreferenceDTO.getFavoriteFoods()));
            newDietPreference.setCreateTime(LocalDateTime.now());
            newDietPreference.setUpdateTime(LocalDateTime.now());

            int result = dietPreferenceMapper.insert(newDietPreference);
            if (result <= 0) {
                throw new RuntimeException("创建饮食偏好记录失败");
            }
        } else {
            // 更新现有记录
            existingDietPreference.setVegetarianType(dietPreferenceDTO.getVegetarianType());
            existingDietPreference.setRestrictions(convertToJsonString(dietPreferenceDTO.getRestrictions()));
            existingDietPreference.setAllergies(convertToJsonString(dietPreferenceDTO.getAllergies()));
            existingDietPreference.setPreferences(convertToJsonString(dietPreferenceDTO.getPreferences()));
            existingDietPreference.setForbiddenFoods(convertToJsonString(dietPreferenceDTO.getForbiddenFoods()));
            existingDietPreference.setFavoriteFoods(convertToJsonString(dietPreferenceDTO.getFavoriteFoods()));
            existingDietPreference.setUpdateTime(LocalDateTime.now());

            int result = dietPreferenceMapper.updateByUserId(existingDietPreference);
            if (result <= 0) {
                throw new RuntimeException("更新饮食偏好记录失败");
            }
        }

        return dietPreferenceDTO;
    }

    /**
     * 更新用户营养目标
     */
    @Override
    public UserNutritionGoalsDTO updateUserNutritionGoals(Long userId, UserNutritionGoalsDTO nutritionGoalsDTO) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 查找现有营养目标记录
        UserNutritionGoals existingNutritionGoals = nutritionGoalsMapper.selectByUserId(userId);

        if (existingNutritionGoals == null) {
            // 创建新记录
            UserNutritionGoals newNutritionGoals = new UserNutritionGoals();
            newNutritionGoals.setUserId(userId);
            newNutritionGoals.setWeightGoal(convertToJsonString(nutritionGoalsDTO.getWeightGoal()));
            newNutritionGoals.setNutritionGoals(convertToJsonString(nutritionGoalsDTO.getNutritionGoals()));
            newNutritionGoals.setDiseaseManagement(convertToJsonString(nutritionGoalsDTO.getDiseaseManagement()));
            newNutritionGoals.setCreateTime(LocalDateTime.now());
            newNutritionGoals.setUpdateTime(LocalDateTime.now());

            int result = nutritionGoalsMapper.insert(newNutritionGoals);
            if (result <= 0) {
                throw new RuntimeException("创建营养目标记录失败");
            }
        } else {
            // 更新现有记录
            existingNutritionGoals.setWeightGoal(convertToJsonString(nutritionGoalsDTO.getWeightGoal()));
            existingNutritionGoals.setNutritionGoals(convertToJsonString(nutritionGoalsDTO.getNutritionGoals()));
            existingNutritionGoals.setDiseaseManagement(convertToJsonString(nutritionGoalsDTO.getDiseaseManagement()));
            existingNutritionGoals.setUpdateTime(LocalDateTime.now());

            int result = nutritionGoalsMapper.updateByUserId(existingNutritionGoals);
            if (result <= 0) {
                throw new RuntimeException("更新营养目标记录失败");
            }
        }

        return nutritionGoalsDTO;
    }

    /**
     * 上传用户头像
     */
    @Override
    public String uploadUserAvatar(Long userId, MultipartFile file) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证文件
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("文件大小不能超过5MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!ALLOWED_IMAGE_TYPES.contains(contentType)) {
            throw new RuntimeException("不支持的文件格式，仅支持JPG、PNG、GIF、WebP格式");
        }

        try {
            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath, "avatars");
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.contains(".")) {
                throw new RuntimeException("文件名无效");
            }
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFilename = "avatar_" + userId + "_" + UUID.randomUUID().toString() + fileExtension;

            // 保存文件
            Path filePath = uploadDir.resolve(newFilename);
            Files.copy(file.getInputStream(), filePath);

            // 生成访问URL
            String avatarUrl = uploadDomain + "/uploads/avatars/" + newFilename;

            // 删除旧头像文件（如果存在）
            if (StringUtils.hasText(user.getAvatar())) {
                deleteOldAvatarFile(user.getAvatar());
            }

            // 更新用户头像URL
            user.setAvatar(avatarUrl);
            user.setUpdateTime(LocalDateTime.now());
            int result = userMapper.updateById(user);
            if (result <= 0) {
                throw new RuntimeException("更新用户头像失败");
            }

            return avatarUrl;

        } catch (IOException e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户头像
     */
    @Override
    public boolean deleteUserAvatar(Long userId) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 删除头像文件
        if (StringUtils.hasText(user.getAvatar())) {
            deleteOldAvatarFile(user.getAvatar());
        }

        // 清空用户头像URL
        user.setAvatar(null);
        user.setUpdateTime(LocalDateTime.now());
        int result = userMapper.updateById(user);
        if (result <= 0) {
            throw new RuntimeException("删除用户头像失败");
        }

        return true;
    }

    // ==================== 私有工具方法 ====================

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON序列化失败: " + e.getMessage());
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     */
    @SuppressWarnings("unchecked")
    private <T> T convertFromJsonString(String jsonString, Class<T> clazz) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON反序列化失败: " + e.getMessage());
        }
    }

    /**
     * 转换为健康状况DTO
     */
    @SuppressWarnings("unchecked")
    private UserHealthStatusDTO convertToHealthStatusDTO(UserHealthStatus healthStatus) {
        UserHealthStatusDTO dto = new UserHealthStatusDTO();
        dto.setChronicDiseases(convertFromJsonString(healthStatus.getChronicDiseases(), List.class));
        dto.setAllergies(convertFromJsonString(healthStatus.getAllergies(), List.class));
        dto.setMedications(convertFromJsonString(healthStatus.getMedications(), List.class));
        dto.setSurgeries(convertFromJsonString(healthStatus.getSurgeries(), List.class));
        dto.setFamilyHistory(convertFromJsonString(healthStatus.getFamilyHistory(), List.class));
        dto.setMedicalReports(convertFromJsonString(healthStatus.getMedicalReports(), List.class));
        return dto;
    }

    /**
     * 转换为饮食偏好DTO
     */
    @SuppressWarnings("unchecked")
    private UserDietPreferenceDTO convertToDietPreferenceDTO(UserDietPreference dietPreference) {
        UserDietPreferenceDTO dto = new UserDietPreferenceDTO();
        dto.setVegetarianType(dietPreference.getVegetarianType());
        dto.setRestrictions(convertFromJsonString(dietPreference.getRestrictions(), List.class));
        dto.setAllergies(convertFromJsonString(dietPreference.getAllergies(), List.class));
        dto.setPreferences(convertFromJsonString(dietPreference.getPreferences(), java.util.Map.class));
        dto.setForbiddenFoods(convertFromJsonString(dietPreference.getForbiddenFoods(), List.class));
        dto.setFavoriteFoods(convertFromJsonString(dietPreference.getFavoriteFoods(), List.class));
        return dto;
    }

    /**
     * 转换为营养目标DTO
     */
    @SuppressWarnings("unchecked")
    private UserNutritionGoalsDTO convertToNutritionGoalsDTO(UserNutritionGoals nutritionGoals) {
        UserNutritionGoalsDTO dto = new UserNutritionGoalsDTO();
        dto.setWeightGoal(convertFromJsonString(nutritionGoals.getWeightGoal(), java.util.Map.class));
        dto.setNutritionGoals(convertFromJsonString(nutritionGoals.getNutritionGoals(), java.util.Map.class));
        dto.setDiseaseManagement(convertFromJsonString(nutritionGoals.getDiseaseManagement(), java.util.Map.class));
        return dto;
    }

    /**
     * 删除旧的头像文件
     */
    private void deleteOldAvatarFile(String avatarUrl) {
        try {
            if (StringUtils.hasText(avatarUrl) && avatarUrl.startsWith(uploadDomain)) {
                // 提取文件路径
                String relativePath = avatarUrl.replace(uploadDomain, "");
                Path filePath = Paths.get(uploadPath + relativePath);

                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                }
            }
        } catch (IOException e) {
            // 删除文件失败不影响主流程，只记录日志
            System.err.println("删除旧头像文件失败: " + e.getMessage());
        }
    }
}
