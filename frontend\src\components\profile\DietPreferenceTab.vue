<template>
  <div class="diet-preference-tab">
    <div class="tab-header">
      <h2>饮食偏好设置</h2>
      <p>设置您的饮食限制、过敏原和口味偏好</p>
    </div>

    <div class="diet-sections">
      <!-- 饮食限制管理 -->
      <div class="diet-section">
        <h3>饮食限制管理</h3>
        
        <div class="restriction-categories">
          <div class="restriction-category">
            <h4>素食类型</h4>
            <div class="restriction-options">
              <label 
                v-for="option in vegetarianOptions" 
                :key="option.value"
                class="restriction-option"
              >
                <input 
                  type="radio" 
                  :value="option.value" 
                  v-model="localDietData.vegetarianType"
                  @change="handleUpdate"
                />
                <span class="option-label">{{ option.label }}</span>
                <span class="option-desc">{{ option.description }}</span>
              </label>
            </div>
          </div>

          <div class="restriction-category">
            <h4>宗教饮食要求</h4>
            <div class="restriction-tags">
              <div 
                v-for="restriction in religiousRestrictions" 
                :key="restriction"
                :class="['restriction-tag', { active: localDietData.restrictions.includes(restriction) }]"
                @click="toggleRestriction(restriction)"
              >
                {{ restriction }}
              </div>
            </div>
          </div>

          <div class="restriction-category">
            <h4>医疗饮食限制</h4>
            <div class="restriction-tags">
              <div 
                v-for="restriction in medicalRestrictions" 
                :key="restriction"
                :class="['restriction-tag', { active: localDietData.restrictions.includes(restriction) }]"
                @click="toggleRestriction(restriction)"
              >
                {{ restriction }}
              </div>
            </div>
          </div>

          <div class="restriction-category">
            <h4>个人饮食原则</h4>
            <div class="restriction-tags">
              <div 
                v-for="principle in personalPrinciples" 
                :key="principle"
                :class="['restriction-tag', { active: localDietData.restrictions.includes(principle) }]"
                @click="toggleRestriction(principle)"
              >
                {{ principle }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 食物过敏原管理 -->
      <div class="diet-section">
        <h3>食物过敏原管理</h3>
        
        <div class="allergen-management">
          <div class="allergen-category">
            <h4>常见过敏原</h4>
            <div class="allergen-grid">
              <div 
                v-for="allergen in commonAllergens" 
                :key="allergen.name"
                :class="['allergen-card', { active: isAllergenSelected(allergen.name) }]"
                @click="toggleAllergen(allergen.name)"
              >
                <span class="allergen-icon">{{ allergen.icon }}</span>
                <span class="allergen-name">{{ allergen.name }}</span>
                <select 
                  v-if="isAllergenSelected(allergen.name)"
                  :value="getAllergenSeverity(allergen.name)"
                  @change="updateAllergenSeverity(allergen.name, $event.target.value)"
                  @click.stop
                  class="severity-select"
                >
                  <option value="mild">轻微</option>
                  <option value="moderate">中等</option>
                  <option value="severe">严重</option>
                </select>
              </div>
            </div>
          </div>

          <div class="custom-allergen">
            <h4>添加其他过敏原</h4>
            <div class="add-allergen-form">
              <input 
                v-model="newAllergen.name" 
                type="text" 
                placeholder="过敏原名称"
              />
              <select v-model="newAllergen.severity">
                <option value="mild">轻微</option>
                <option value="moderate">中等</option>
                <option value="severe">严重</option>
              </select>
              <textarea 
                v-model="newAllergen.reaction" 
                placeholder="过敏反应描述（可选）"
                rows="2"
              ></textarea>
              <button @click="addCustomAllergen" class="add-btn">添加过敏原</button>
            </div>
          </div>

          <div class="emergency-contact">
            <h4>紧急联系人</h4>
            <div class="contact-form">
              <input 
                v-model="localDietData.emergencyContact.name" 
                type="text" 
                placeholder="联系人姓名"
                @input="handleUpdate"
              />
              <input 
                v-model="localDietData.emergencyContact.phone" 
                type="tel" 
                placeholder="联系电话"
                @input="handleUpdate"
              />
              <select 
                v-model="localDietData.emergencyContact.relationship"
                @change="handleUpdate"
              >
                <option value="">关系</option>
                <option value="family">家人</option>
                <option value="friend">朋友</option>
                <option value="colleague">同事</option>
                <option value="doctor">医生</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 口味偏好设置 -->
      <div class="diet-section">
        <h3>口味偏好设置</h3>
        
        <div class="taste-preferences">
          <div class="taste-category">
            <h4>口味偏好</h4>
            <div class="taste-sliders">
              <div 
                v-for="taste in tasteTypes" 
                :key="taste.key"
                class="taste-slider"
              >
                <label>{{ taste.label }}</label>
                <div class="slider-container">
                  <span class="slider-label">不喜欢</span>
                  <input 
                    type="range" 
                    min="1" 
                    max="5" 
                    :value="localDietData.preferences[taste.key] || 3"
                    @input="updateTastePreference(taste.key, $event.target.value)"
                    class="taste-range"
                  />
                  <span class="slider-label">很喜欢</span>
                </div>
                <div class="taste-level">{{ getTasteLevel(localDietData.preferences[taste.key] || 3) }}</div>
              </div>
            </div>
          </div>

          <div class="texture-category">
            <h4>食物质地偏好</h4>
            <div class="texture-tags">
              <div 
                v-for="texture in textureOptions" 
                :key="texture"
                :class="['texture-tag', { active: localDietData.preferences.textures?.includes(texture) }]"
                @click="toggleTexture(texture)"
              >
                {{ texture }}
              </div>
            </div>
          </div>

          <div class="temperature-category">
            <h4>温度偏好</h4>
            <div class="temperature-options">
              <label 
                v-for="temp in temperatureOptions" 
                :key="temp.value"
                class="temperature-option"
              >
                <input 
                  type="radio" 
                  :value="temp.value" 
                  v-model="localDietData.preferences.temperature"
                  @change="handleUpdate"
                />
                <span>{{ temp.label }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 禁忌和喜爱食物 -->
      <div class="diet-section">
        <h3>食物管理</h3>
        
        <div class="food-management">
          <div class="food-category">
            <h4>禁忌食物</h4>
            <div class="food-list">
              <div 
                v-for="(food, index) in localDietData.forbiddenFoods" 
                :key="index"
                class="food-item forbidden"
              >
                <span>{{ food }}</span>
                <button @click="removeForbiddenFood(index)" class="remove-btn">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="add-food">
              <input 
                v-model="newForbiddenFood" 
                type="text" 
                placeholder="添加禁忌食物"
                @keyup.enter="addForbiddenFood"
              />
              <button @click="addForbiddenFood" class="add-btn">添加</button>
            </div>
          </div>

          <div class="food-category">
            <h4>喜爱食物</h4>
            <div class="food-list">
              <div 
                v-for="(food, index) in localDietData.favoriteFoods" 
                :key="index"
                class="food-item favorite"
              >
                <span>{{ food }}</span>
                <button @click="removeFavoriteFood(index)" class="remove-btn">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="add-food">
              <input 
                v-model="newFavoriteFood" 
                type="text" 
                placeholder="添加喜爱食物"
                @keyup.enter="addFavoriteFood"
              />
              <button @click="addFavoriteFood" class="add-btn">添加</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
const props = defineProps<{
  dietData: any
}>()

// Emits
const emit = defineEmits<{
  update: []
}>()

// 本地数据
const localDietData = ref({ ...props.dietData })

// 新增数据的临时对象
const newAllergen = ref({
  name: '',
  severity: 'mild',
  reaction: ''
})

const newForbiddenFood = ref('')
const newFavoriteFood = ref('')

// 选项数据
const vegetarianOptions = [
  { value: 'none', label: '无限制', description: '可以食用所有食物' },
  { value: 'pescatarian', label: '鱼素', description: '不吃肉类，但可以吃鱼' },
  { value: 'vegetarian', label: '蛋奶素', description: '不吃肉类和鱼类，但可以吃蛋奶' },
  { value: 'vegan', label: '严格素食', description: '不吃任何动物制品' }
]

const religiousRestrictions = ['清真', '犹太洁食', '印度教素食', '佛教素食']
const medicalRestrictions = ['无麸质', '低钠', '低糖', '低脂', '低嘌呤', '无乳糖']
const personalPrinciples = ['有机食品', '本地食材', '非转基因', '无添加剂', '公平贸易']

const commonAllergens = [
  { name: '花生', icon: '🥜' },
  { name: '坚果', icon: '🌰' },
  { name: '海鲜', icon: '🦐' },
  { name: '牛奶', icon: '🥛' },
  { name: '鸡蛋', icon: '🥚' },
  { name: '大豆', icon: '🫘' },
  { name: '小麦', icon: '🌾' },
  { name: '芝麻', icon: '🫘' }
]

const tasteTypes = [
  { key: 'sweet', label: '甜味' },
  { key: 'sour', label: '酸味' },
  { key: 'spicy', label: '辣味' },
  { key: 'salty', label: '咸味' },
  { key: 'bitter', label: '苦味' },
  { key: 'umami', label: '鲜味' }
]

const textureOptions = ['软嫩', '爽脆', '有嚼劲', '顺滑', '粗糙', '细腻']
const temperatureOptions = [
  { value: 'hot', label: '热食' },
  { value: 'warm', label: '温食' },
  { value: 'room', label: '常温' },
  { value: 'cold', label: '冷食' },
  { value: 'mixed', label: '无偏好' }
]

// 监听props变化
watch(() => props.dietData, (newVal) => {
  localDietData.value = { ...newVal }
}, { deep: true })

// 切换饮食限制
const toggleRestriction = (restriction: string) => {
  const index = localDietData.value.restrictions.indexOf(restriction)
  if (index > -1) {
    localDietData.value.restrictions.splice(index, 1)
  } else {
    localDietData.value.restrictions.push(restriction)
  }
  handleUpdate()
}

// 检查过敏原是否被选中
const isAllergenSelected = (allergen: string) => {
  return localDietData.value.allergies.some(a => a.name === allergen)
}

// 切换过敏原
const toggleAllergen = (allergen: string) => {
  const index = localDietData.value.allergies.findIndex(a => a.name === allergen)
  if (index > -1) {
    localDietData.value.allergies.splice(index, 1)
  } else {
    localDietData.value.allergies.push({
      name: allergen,
      severity: 'mild',
      reaction: ''
    })
  }
  handleUpdate()
}

// 获取过敏原严重程度
const getAllergenSeverity = (allergen: string) => {
  const found = localDietData.value.allergies.find(a => a.name === allergen)
  return found?.severity || 'mild'
}

// 更新过敏原严重程度
const updateAllergenSeverity = (allergen: string, severity: string) => {
  const found = localDietData.value.allergies.find(a => a.name === allergen)
  if (found) {
    found.severity = severity
    handleUpdate()
  }
}

// 添加自定义过敏原
const addCustomAllergen = () => {
  if (!newAllergen.value.name.trim()) return
  
  localDietData.value.allergies.push({ ...newAllergen.value })
  
  // 重置表单
  newAllergen.value = {
    name: '',
    severity: 'mild',
    reaction: ''
  }
  
  handleUpdate()
}

// 更新口味偏好
const updateTastePreference = (taste: string, value: string) => {
  localDietData.value.preferences[taste] = parseInt(value)
  handleUpdate()
}

// 获取口味等级文本
const getTasteLevel = (level: number) => {
  const levels = ['', '很不喜欢', '不喜欢', '一般', '喜欢', '很喜欢']
  return levels[level] || '一般'
}

// 切换质地偏好
const toggleTexture = (texture: string) => {
  if (!localDietData.value.preferences.textures) {
    localDietData.value.preferences.textures = []
  }
  
  const index = localDietData.value.preferences.textures.indexOf(texture)
  if (index > -1) {
    localDietData.value.preferences.textures.splice(index, 1)
  } else {
    localDietData.value.preferences.textures.push(texture)
  }
  handleUpdate()
}

// 添加禁忌食物
const addForbiddenFood = () => {
  if (!newForbiddenFood.value.trim()) return
  if (!localDietData.value.forbiddenFoods.includes(newForbiddenFood.value)) {
    localDietData.value.forbiddenFoods.push(newForbiddenFood.value)
    newForbiddenFood.value = ''
    handleUpdate()
  }
}

// 删除禁忌食物
const removeForbiddenFood = (index: number) => {
  localDietData.value.forbiddenFoods.splice(index, 1)
  handleUpdate()
}

// 添加喜爱食物
const addFavoriteFood = () => {
  if (!newFavoriteFood.value.trim()) return
  if (!localDietData.value.favoriteFoods.includes(newFavoriteFood.value)) {
    localDietData.value.favoriteFoods.push(newFavoriteFood.value)
    newFavoriteFood.value = ''
    handleUpdate()
  }
}

// 删除喜爱食物
const removeFavoriteFood = (index: number) => {
  localDietData.value.favoriteFoods.splice(index, 1)
  handleUpdate()
}

// 处理数据更新
const handleUpdate = () => {
  emit('update')
}

// 监听本地数据变化，同步到父组件
watch(localDietData, (newVal) => {
  Object.assign(props.dietData, newVal)
}, { deep: true })

// 初始化数据结构
if (!localDietData.value.restrictions) localDietData.value.restrictions = []
if (!localDietData.value.allergies) localDietData.value.allergies = []
if (!localDietData.value.preferences) localDietData.value.preferences = {}
if (!localDietData.value.forbiddenFoods) localDietData.value.forbiddenFoods = []
if (!localDietData.value.favoriteFoods) localDietData.value.favoriteFoods = []
if (!localDietData.value.emergencyContact) localDietData.value.emergencyContact = {}
if (!localDietData.value.vegetarianType) localDietData.value.vegetarianType = 'none'
</script>

<style scoped>
.diet-preference-tab {
  max-width: 900px;
}

.tab-header {
  margin-bottom: 2rem;
}

.tab-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.tab-header p {
  margin: 0;
  color: #64748b;
}

.diet-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.diet-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.diet-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.diet-section h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
}

/* 饮食限制样式 */
.restriction-categories {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.restriction-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.restriction-option {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.restriction-option:hover {
  border-color: #16a085;
}

.restriction-option input[type="radio"] {
  margin-bottom: 0.5rem;
}

.option-label {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.option-desc {
  font-size: 0.9rem;
  color: #64748b;
}

.restriction-tags,
.texture-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.restriction-tag,
.texture-tag {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.restriction-tag:hover,
.texture-tag:hover {
  border-color: #16a085;
}

.restriction-tag.active,
.texture-tag.active {
  background: #16a085;
  color: white;
  border-color: #16a085;
}

/* 过敏原管理样式 */
.allergen-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.allergen-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.allergen-card:hover {
  border-color: #16a085;
}

.allergen-card.active {
  border-color: #16a085;
  background: #f0fdfa;
}

.allergen-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.allergen-name {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.severity-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.8rem;
}

.add-allergen-form,
.contact-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: start;
}

.add-allergen-form input,
.add-allergen-form select,
.add-allergen-form textarea,
.contact-form input,
.contact-form select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
}

/* 口味偏好样式 */
.taste-sliders {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.taste-slider {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.taste-slider label {
  font-weight: 500;
  color: #374151;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.slider-label {
  font-size: 0.8rem;
  color: #64748b;
  min-width: 60px;
}

.taste-range {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.taste-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: #16a085;
  border-radius: 50%;
  cursor: pointer;
}

.taste-level {
  text-align: center;
  font-size: 0.9rem;
  color: #16a085;
  font-weight: 500;
}

.temperature-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.temperature-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
}

/* 食物管理样式 */
.food-management {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.food-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.food-item.forbidden {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.food-item.favorite {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.add-food {
  display: flex;
  gap: 0.5rem;
}

.add-food input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

.add-btn {
  padding: 0.75rem 1rem;
  background: #16a085;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s ease;
}

.add-btn:hover {
  background: #138d75;
}

.remove-btn {
  padding: 0.25rem;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #ef4444;
}

.remove-btn svg {
  width: 16px;
  height: 16px;
}

@media (max-width: 768px) {
  .allergen-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .food-management {
    grid-template-columns: 1fr;
  }
  
  .add-allergen-form,
  .contact-form {
    grid-template-columns: 1fr;
  }
}
</style>
