package com.meals.mapper;

import com.meals.entity.UserHealthStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户健康状况数据访问层接口
 * 提供用户健康状况相关的数据库操作方法
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserHealthStatusMapper {

    /**
     * 插入用户健康状况记录
     * 
     * @param healthStatus 健康状况信息
     * @return 影响的行数
     */
    int insert(UserHealthStatus healthStatus);

    /**
     * 根据用户ID查询健康状况
     * 
     * @param userId 用户ID
     * @return 健康状况信息，如果不存在返回null
     */
    UserHealthStatus selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户健康状况
     * 
     * @param healthStatus 健康状况信息
     * @return 影响的行数
     */
    int updateByUserId(UserHealthStatus healthStatus);

    /**
     * 根据用户ID删除健康状况记录
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
