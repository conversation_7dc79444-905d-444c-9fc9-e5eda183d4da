<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meals.mapper.UserNutritionGoalsMapper">

    <!-- 用户营养目标结果映射 -->
    <resultMap id="BaseResultMap" type="UserNutritionGoals">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="weight_goal" property="weightGoal" jdbcType="VARCHAR"/>
        <result column="nutrition_goals" property="nutritionGoals" jdbcType="VARCHAR"/>
        <result column="disease_management" property="diseaseManagement" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, weight_goal, nutrition_goals, disease_management, create_time, update_time
    </sql>

    <!-- 插入用户营养目标 -->
    <insert id="insert" parameterType="UserNutritionGoals" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_nutrition_goals (user_id, weight_goal, nutrition_goals, disease_management, create_time, update_time)
        VALUES (#{userId}, #{weightGoal}, #{nutritionGoals}, #{diseaseManagement}, NOW(), NOW())
    </insert>

    <!-- 根据用户ID查询营养目标 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_nutrition_goals
        WHERE user_id = #{userId}
    </select>

    <!-- 更新用户营养目标 -->
    <update id="updateByUserId" parameterType="UserNutritionGoals">
        UPDATE user_nutrition_goals
        SET
            weight_goal = #{weightGoal},
            nutrition_goals = #{nutritionGoals},
            disease_management = #{diseaseManagement},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 根据用户ID删除营养目标记录 -->
    <delete id="deleteByUserId">
        DELETE FROM user_nutrition_goals
        WHERE user_id = #{userId}
    </delete>

</mapper>
