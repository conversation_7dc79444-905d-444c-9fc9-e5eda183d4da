package com.meals.entity;

import java.time.LocalDateTime;

/**
 * 用户饮食偏好实体类
 * 对应数据库user_diet_preference表
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserDietPreference {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 素食类型：none-无限制，pescatarian-鱼素，vegetarian-蛋奶素，vegan-严格素食
     */
    private String vegetarianType;

    /**
     * 饮食限制（JSON格式）
     */
    private String restrictions;

    /**
     * 食物过敏（JSON格式）
     */
    private String allergies;

    /**
     * 口味偏好（JSON格式）
     */
    private String preferences;

    /**
     * 禁忌食物（JSON格式）
     */
    private String forbiddenFoods;

    /**
     * 偏好食物（JSON格式）
     */
    private String favoriteFoods;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // ==================== Getter和Setter方法 ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getVegetarianType() {
        return vegetarianType;
    }

    public void setVegetarianType(String vegetarianType) {
        this.vegetarianType = vegetarianType;
    }

    public String getRestrictions() {
        return restrictions;
    }

    public void setRestrictions(String restrictions) {
        this.restrictions = restrictions;
    }

    public String getAllergies() {
        return allergies;
    }

    public void setAllergies(String allergies) {
        this.allergies = allergies;
    }

    public String getPreferences() {
        return preferences;
    }

    public void setPreferences(String preferences) {
        this.preferences = preferences;
    }

    public String getForbiddenFoods() {
        return forbiddenFoods;
    }

    public void setForbiddenFoods(String forbiddenFoods) {
        this.forbiddenFoods = forbiddenFoods;
    }

    public String getFavoriteFoods() {
        return favoriteFoods;
    }

    public void setFavoriteFoods(String favoriteFoods) {
        this.favoriteFoods = favoriteFoods;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
