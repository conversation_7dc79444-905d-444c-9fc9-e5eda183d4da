package com.meals.mapper;

import com.meals.entity.UserNutritionGoals;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户营养目标数据访问层接口
 * 提供用户营养目标相关的数据库操作方法
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserNutritionGoalsMapper {

    /**
     * 插入用户营养目标记录
     * 
     * @param nutritionGoals 营养目标信息
     * @return 影响的行数
     */
    int insert(UserNutritionGoals nutritionGoals);

    /**
     * 根据用户ID查询营养目标
     * 
     * @param userId 用户ID
     * @return 营养目标信息，如果不存在返回null
     */
    UserNutritionGoals selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户营养目标
     * 
     * @param nutritionGoals 营养目标信息
     * @return 影响的行数
     */
    int updateByUserId(UserNutritionGoals nutritionGoals);

    /**
     * 根据用户ID删除营养目标记录
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
