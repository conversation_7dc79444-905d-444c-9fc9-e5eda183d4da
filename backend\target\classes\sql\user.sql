-- =============================================
-- 膳食营养分析平台 - 用户表设计
-- 创建时间: 2025-08-05
-- 作者: Meals Team
-- 版本: 1.0.0
-- =============================================

-- 使用数据库
USE `meals`;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `user`;

-- 创建用户表
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键自增',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名，唯一',
    `password` VARCHAR(255) NOT NULL COMMENT '密码，加密存储（格式：盐值:加密密码）',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    `name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `birth_date` DATE DEFAULT NULL COMMENT '出生日期',
    `age` INT DEFAULT NULL COMMENT '用户年龄',
    `gender` TINYINT DEFAULT NULL COMMENT '用户性别：0-女，1-男',
    `height` DECIMAL(5,2) DEFAULT NULL COMMENT '身高（厘米）',
    `weight` DECIMAL(5,2) DEFAULT NULL COMMENT '体重（公斤）',
    `body_fat` DECIMAL(5,2) DEFAULT NULL COMMENT '体脂率（%）',
    `muscle_mass` DECIMAL(5,2) DEFAULT NULL COMMENT '肌肉量（公斤）',
    `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '用户状态：0-禁用，1-启用',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';


