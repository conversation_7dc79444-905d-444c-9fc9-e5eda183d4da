package com.meals.dto;

import java.util.List;
import java.util.Map;

/**
 * 用户健康状况DTO
 * 用于前端和后端之间的健康状况数据传输
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UserHealthStatusDTO {

    /**
     * 慢性病史列表
     */
    private List<Map<String, Object>> chronicDiseases;

    /**
     * 过敏信息列表
     */
    private List<String> allergies;

    /**
     * 用药记录列表
     */
    private List<Map<String, Object>> medications;

    /**
     * 手术史列表
     */
    private List<Map<String, Object>> surgeries;

    /**
     * 家族病史列表
     */
    private List<Map<String, Object>> familyHistory;

    /**
     * 体检报告列表
     */
    private List<Map<String, Object>> medicalReports;

    // ==================== Getter和Setter方法 ====================

    public List<Map<String, Object>> getChronicDiseases() {
        return chronicDiseases;
    }

    public void setChronicDiseases(List<Map<String, Object>> chronicDiseases) {
        this.chronicDiseases = chronicDiseases;
    }

    public List<String> getAllergies() {
        return allergies;
    }

    public void setAllergies(List<String> allergies) {
        this.allergies = allergies;
    }

    public List<Map<String, Object>> getMedications() {
        return medications;
    }

    public void setMedications(List<Map<String, Object>> medications) {
        this.medications = medications;
    }

    public List<Map<String, Object>> getSurgeries() {
        return surgeries;
    }

    public void setSurgeries(List<Map<String, Object>> surgeries) {
        this.surgeries = surgeries;
    }

    public List<Map<String, Object>> getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(List<Map<String, Object>> familyHistory) {
        this.familyHistory = familyHistory;
    }

    public List<Map<String, Object>> getMedicalReports() {
        return medicalReports;
    }

    public void setMedicalReports(List<Map<String, Object>> medicalReports) {
        this.medicalReports = medicalReports;
    }
}
