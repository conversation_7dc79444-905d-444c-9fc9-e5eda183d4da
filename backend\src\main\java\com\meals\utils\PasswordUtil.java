package com.meals.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码加密工具类
 * 提供密码的加密和验证功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class PasswordUtil {
    
    /**
     * 盐值长度
     */
    private static final int SALT_LENGTH = 16;
    
    /**
     * 加密算法
     */
    private static final String ALGORITHM = "SHA-256";
    
    /**
     * 生成随机盐值
     * 
     * @return Base64编码的盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 使用盐值加密密码
     * 
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            // 将盐值和密码组合
            String saltedPassword = salt + password;
            byte[] hashedPassword = md.digest(saltedPassword.getBytes());
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 加密密码（自动生成盐值）
     * 
     * @param password 原始密码
     * @return 格式为 "盐值:加密密码" 的字符串
     */
    public static String encryptPassword(String password) {
        String salt = generateSalt();
        String encryptedPassword = encryptPassword(password, salt);
        return salt + ":" + encryptedPassword;
    }
    
    /**
     * 验证密码是否正确
     * 
     * @param password 用户输入的密码
     * @param storedPassword 数据库存储的密码（格式：盐值:加密密码）
     * @return 密码是否正确
     */
    public static boolean verifyPassword(String password, String storedPassword) {
        try {
            // 分离盐值和加密密码
            String[] parts = storedPassword.split(":");
            if (parts.length != 2) {
                return false;
            }
            
            String salt = parts[0];
            String encryptedPassword = parts[1];
            
            // 使用相同的盐值加密输入的密码
            String inputEncrypted = encryptPassword(password, salt);
            
            // 比较加密结果
            return encryptedPassword.equals(inputEncrypted);
        } catch (Exception e) {
            return false;
        }
    }
}
