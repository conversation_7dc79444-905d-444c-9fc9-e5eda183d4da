package com.meals.mapper;

import com.meals.entity.UserDietPreference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户饮食偏好数据访问层接口
 * 提供用户饮食偏好相关的数据库操作方法
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserDietPreferenceMapper {

    /**
     * 插入用户饮食偏好记录
     * 
     * @param dietPreference 饮食偏好信息
     * @return 影响的行数
     */
    int insert(UserDietPreference dietPreference);

    /**
     * 根据用户ID查询饮食偏好
     * 
     * @param userId 用户ID
     * @return 饮食偏好信息，如果不存在返回null
     */
    UserDietPreference selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户饮食偏好
     * 
     * @param dietPreference 饮食偏好信息
     * @return 影响的行数
     */
    int updateByUserId(UserDietPreference dietPreference);

    /**
     * 根据用户ID删除饮食偏好记录
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
