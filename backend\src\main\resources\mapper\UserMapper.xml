<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meals.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="BaseResultMap" type="User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="birth_date" property="birthDate" jdbcType="DATE"/>
        <result column="age" property="age" jdbcType="INTEGER"/>
        <result column="gender" property="gender" jdbcType="INTEGER"/>
        <result column="height" property="height" jdbcType="DOUBLE"/>
        <result column="weight" property="weight" jdbcType="DOUBLE"/>
        <result column="body_fat" property="bodyFat" jdbcType="DOUBLE"/>
        <result column="muscle_mass" property="muscleMass" jdbcType="DOUBLE"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, phone, name, birth_date, age, gender, height, weight,
        body_fat, muscle_mass, avatar, status, deleted, create_time, update_time
    </sql>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (username, password, email, phone, name, birth_date, age, gender, height, weight, body_fat, muscle_mass, avatar, status, deleted, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{name}, #{birthDate}, #{age}, #{gender}, #{height}, #{weight}, #{bodyFat}, #{muscleMass}, #{avatar}, #{status}, #{deleted}, NOW(), NOW())
    </insert>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 更新用户信息 -->
    <update id="updateById" parameterType="User">
        UPDATE user
        SET
            username = #{username},
            password = #{password},
            email = #{email},
            phone = #{phone},
            name = #{name},
            birth_date = #{birthDate},
            age = #{age},
            gender = #{gender},
            height = #{height},
            weight = #{weight},
            body_fat = #{bodyFat},
            muscle_mass = #{muscleMass},
            avatar = #{avatar},
            status = #{status},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 逻辑删除用户 -->
    <update id="deleteById">
        UPDATE user
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 查询条件片段 -->
    <sql id="Where_Clause">
        <where>
            deleted = 0
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone = #{phone}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </sql>

    <!-- 根据用户名查询用户信息 -->
    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM user 
        WHERE username = #{username} 
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据邮箱查询用户信息 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM user 
        WHERE email = #{email} 
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据手机号查询用户信息 -->
    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM user 
        WHERE phone = #{phone} 
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查用户名是否已存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0 
        FROM user 
        WHERE username = #{username} 
        AND deleted = 0
    </select>

    <!-- 检查邮箱是否已存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0 
        FROM user 
        WHERE email = #{email} 
        AND deleted = 0
    </select>

    <!-- 检查手机号是否已存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(*) > 0 
        FROM user 
        WHERE phone = #{phone} 
        AND deleted = 0
    </select>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询活跃用户列表 -->
    <select id="findActiveUsers" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM user 
        WHERE status = 1 
        AND deleted = 0 
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据多个条件查询用户 -->
    <select id="findUsersByConditions" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM user
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsersByStatus" resultType="long">
        SELECT COUNT(*) 
        FROM user 
        WHERE deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

</mapper>
